const AbstractWorker = require('../AbstractWorker');
const {
    SALES_HUB_WEBHOOK_QUEUE,
} = require('../../api/constants/workers-queue');
const SalesHubOrderWebhookHandler = require('../../api/services/sales-hub/webhook/SalesHubOrderWebhookHandler');
const { WEBHOOK_EVENT_TYPES } = require('../../api/constants/sales-hub');

class SalesHubWebhookWorker extends AbstractWorker {
    static queueName() {
        return SALES_HUB_WEBHOOK_QUEUE;
    }

    async doJob(job) {
        const webhookData = job.data;
        const eventType = webhookData.type;

        switch (eventType) {
            // Occurs whenever a new charge is created and is successful.
            case WEBHOOK_EVENT_TYPES.ORDER_CREATED:
            case WEBHOOK_EVENT_TYPES.ORDER_UPDATED:
            case WEBHOOK_EVENT_TYPES.ORDER_PAID:
                return SalesHubOrderWebhookHandler.handle(webhookData)
        }
    }
}

module.exports = SalesHubWebhookWorker;
