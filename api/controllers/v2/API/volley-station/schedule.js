module.exports = {
    friendlyName: 'Get event schedule for VolleyStation',
    description: 'Returns paginated schedule data for a specific event',

    inputs: {
        eventId: {
            type: 'number',
            required: true,
            description: 'The ID of the event'
        },
        page: {
            type: 'number',
            defaultsTo: 1,
            min: 1,
            description: 'Page number for pagination'
        },
        limit: {
            type: 'number',
            defaultsTo: 50,
            min: 1,
            max: 100,
            description: 'Items per page (max 100)'
        }
    },

    exits: {
        success: {
            statusCode: 200
        }
    },

    fn: async function (inputs, exits) {
        try {
            const paginatedSchedule = await VolleyStationService.getPaginatedSchedule(
                inputs.eventId,
                inputs.page,
                inputs.limit
            );

            return exits.success(paginatedSchedule);

        } catch (err) {
            sails.log.error('VolleyStation schedule error:', err);
            throw { message: 'Server Internal Error' };
        }
    }
};
