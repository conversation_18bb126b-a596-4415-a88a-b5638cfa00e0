const { RateLimiterRedis } = require('rate-limiter-flexible');
const crypto = require('crypto');
const redis = require('redis');

/**
 * VolleyStation Rate Limiting Policy
 * 
 * Implements Redis-based rate limiting for VolleyStation API endpoints.
 * Uses authentication tokens as the limiting identifier.
 * 
 * Rate Limit: 1000 requests per minute per authentication token
 * Redis Key Pattern: rate-limit:volley-station:{token_hash}
 */

let rateLimiter = null;
let redisClient = null;

/**
 * Initialize the rate limiter with Redis connection
 */
function initializeRateLimiter() {
    if (rateLimiter) {
        return rateLimiter;
    }

    try {
        // Create Redis client using existing configuration
        redisClient = redis.createClient(sails.config.connections.redis);
        
        // Handle Redis connection errors
        redisClient.on('error', (err) => {
            sails.log.error('VolleyStation Rate Limiter Redis Error:', err);
        });

        // Initialize rate limiter with configuration
        const config = sails.config.volleyStation.rateLimit;
        rateLimiter = new RateLimiterRedis({
            storeClient: redisClient,
            keyPrefix: 'rate-limit:volley-station:',
            points: config.points,           // Number of requests
            duration: config.duration,       // Per duration in seconds
            blockDuration: config.blockDuration, // Block duration in seconds
            execEvenly: true,                // Spread requests evenly across duration
        });

        sails.log.info('VolleyStation Rate Limiter initialized successfully');
        return rateLimiter;
    } catch (error) {
        sails.log.error('Failed to initialize VolleyStation Rate Limiter:', error);
        return null;
    }
}

/**
 * Generate consistent hash for authentication token
 * @param {string} token - Authentication token
 * @returns {string} - 8-character hash
 */
function generateTokenHash(token) {
    return crypto
        .createHash('sha256')
        .update(token)
        .digest('hex')
        .substring(0, 8);
}

/**
 * Extract authentication token from request
 * @param {Object} req - Request object
 * @returns {string|null} - Authentication token or null if not found
 */
function extractToken(req) {
    const authHeader = req.get('Authorization');
    if (!authHeader) {
        return null;
    }
    
    // Token should match the configured API key
    if (authHeader === sails.config.volleyStation.apiKey) {
        return authHeader;
    }
    
    return null;
}

/**
 * Create rate limit error response
 * @param {Object} rateLimiterRes - Rate limiter response
 * @returns {Object} - Formatted error response
 */
function createRateLimitResponse(rateLimiterRes) {
    const resetTime = new Date(Date.now() + rateLimiterRes.msBeforeNext);
    
    return {
        error: 'Rate limit exceeded',
        message: 'Too many requests for this API token',
        retryAfter: Math.round(rateLimiterRes.msBeforeNext / 1000),
        limit: {
            requests: sails.config.volleyStation.rateLimit.points,
            window: `${sails.config.volleyStation.rateLimit.duration} seconds`,
            remaining: rateLimiterRes.remainingPoints || 0,
            resetTime: resetTime.toISOString()
        }
    };
}

/**
 * VolleyStation Rate Limiting Policy
 * 
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 */
module.exports = async function volleyStationRateLimit(req, res, next) {
    try {
        // Extract authentication token
        const token = extractToken(req);
        if (!token) {
            // No token found - this should be handled by auth policy
            // Allow request to proceed to auth policy for proper error handling
            return next();
        }

        // Initialize rate limiter if not already done
        const limiter = initializeRateLimiter();
        if (!limiter) {
            // Rate limiter initialization failed - fail open
            sails.log.warn('Rate limiter not available, allowing request through');
            return next();
        }

        // Generate consistent hash for the token
        const tokenHash = generateTokenHash(token);

        // Apply rate limiting
        const rateLimiterRes = await limiter.consume(tokenHash);
        
        // Add rate limit information to request for potential use by controllers
        req.rateLimit = {
            limit: sails.config.volleyStation.rateLimit.points,
            remaining: rateLimiterRes.remainingPoints,
            resetTime: new Date(Date.now() + rateLimiterRes.msBeforeNext),
            retryAfter: null
        };

        // Request is within limits, proceed
        return next();

    } catch (rateLimiterRes) {
        // Rate limit exceeded
        if (rateLimiterRes instanceof Error) {
            // Actual error occurred
            sails.log.error('VolleyStation Rate Limiter Error:', rateLimiterRes);
            // Fail open - allow request through
            return next();
        }

        // Rate limit exceeded - rateLimiterRes contains limit information
        const errorResponse = createRateLimitResponse(rateLimiterRes);
        
        // Set appropriate headers
        res.set({
            'Retry-After': errorResponse.retryAfter,
            'X-RateLimit-Limit': errorResponse.limit.requests,
            'X-RateLimit-Remaining': errorResponse.limit.remaining,
            'X-RateLimit-Reset': errorResponse.limit.resetTime
        });

        // Log rate limit violation
        sails.log.warn('VolleyStation Rate Limit Exceeded:', {
            tokenHash: generateTokenHash(extractToken(req)),
            path: req.path,
            ip: req.ip,
            userAgent: req.get('User-Agent')
        });

        // Return 429 Too Many Requests
        return res.status(429).json(errorResponse);
    }
};

/**
 * Graceful shutdown - close Redis connection
 */
process.on('SIGTERM', () => {
    if (redisClient) {
        redisClient.quit();
    }
});

process.on('SIGINT', () => {
    if (redisClient) {
        redisClient.quit();
    }
});
