module.exports = async function volleyStationEventAccess(req, res, next) {
    const eventId = parseInt(req.params.eventId);

    if (!eventId || isNaN(eventId)) {
        return res.validation('Invalid event identifier passed');
    }

    try {
        const enabled = await VolleyStationService.isEnabledForEvent(eventId);
        if (!enabled) {
            return res.status(403).json({
                error: 'Event not available for VolleyStation integration'
            });
        }
        next();
    } catch (err) {
        sails.log.error('VolleyStation event access error:', err);
        return res.status(500).json({
            error: 'Internal server error',
            message: 'Unable to verify event access'
        });
    }
};
