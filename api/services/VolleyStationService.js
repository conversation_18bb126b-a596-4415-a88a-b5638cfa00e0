class VolleyStationService {
    constructor() {}

    /**
     * Check if event has VolleyStation enabled
     * @param {number} eventId - The event ID
     * @returns {Promise<boolean>} Whether VolleyStation is enabled
     */
    async isEnabledForEvent(eventId) {
        const query = squel
            .select()
            .from('event')
            .field('event_id')
            .where('event_id = ?', eventId)
            .where('enable_volley_station = ?', true);

        const { rows } = await Db.query(query);
        return rows.length > 0;
    }

    /**
     * Get schedule data for a specific event with pagination
     * @param {number} eventId - The event ID
     * @param {number} page - Page number (1-based)
     * @param {number} limit - Items per page
     * @returns {Promise<Object>} Complete pagination response with data and metadata
     */
    async getPaginatedSchedule(eventId, page, limit) {
        const cacheKey = `volley-station:schedule:${eventId}:${page}:${limit}`;

        return Cache.getResult(
            cacheKey,
            () => this._getPaginatedScheduleData(eventId, page, limit),
            {
                ttl: Cache.TTL_DEFAULT,
                tags: [
                    Cache.tag.dbTable('event', { event_id: eventId }),
                    Cache.tag.dbTable('matches'),
                ],
            }
        );
    }

    /**
     * Get schedule data for a specific event with pagination (extracted for caching)
     * @private
     */
    async _getPaginatedScheduleData(eventId, page, limit) {
        const offset = (page - 1) * limit;
        const query = squel
            .select()
            .from('event', 'e')
            .field('d.gender')
            .field('d.name', 'division_name')
            .field('d.division_id')
            .field('m.match_id', 'match_uuid')
            .field('m.event_id', 'event')
            .field(`m.event_id || '_' || m.division_short_name || '_' || m.display_name`, 'match_id')
            .field('m.division_short_name', 'div')
            .field('m.day')
            .field(`to_char(m.secs_start AT TIME ZONE e.timezone, 'YYYY-MM-DD"T"HH24:MI:SS"Z"')`, 'date_time')
            .field('c.sort_priority', 'court')
            .field('c.short_name', 'court_alpha')
            .field('pb.display_name', 'pool')
            .field('m.team1_roster_id')
            .field('m.team2_roster_id')
            .field('m.ref_roster_id')
            .field('t1.team_name', 'team_1_name')
            .field('t2.team_name', 'team_2_name')
            .field('tr.team_name', 'ref_name')
            .field('t1.master_team_id', 'master_team_id_1')
            .field('t2.master_team_id', 'master_team_id_2')
            .field('m.type', 'match_type')
            .field(`(
                SELECT jsonb_object_agg(key, value)
                FROM jsonb_each_text(m.results)
                WHERE key LIKE 'set%'
            )`, 'results')
            .field(`u.first || ' ' || u.last`, 'official')
            .field('COUNT(*) OVER()', 'total_count')
            .join('division', 'd', 'd.event_id = e.event_id')
            .join('matches', 'm', squel.expr()
                .and('m.event_id = d.event_id')
                .and('m.division_id = d.division_id'))
            .join('poolbrackets', 'pb', 'pb.uuid = m.pool_bracket_id')
            .join('courts', 'c', 'c.uuid = m.court_id')
            .left_join('roster_team', 't1', squel.expr()
                .and('t1.roster_team_id = m.team1_roster_id')
                .and('t1.event_id = d.event_id')
                .and('t1.division_id = d.division_id')
                .and('t1.deleted IS NULL')
                .and('t1.status_entry = 12'))
            .left_join('roster_team', 't2', squel.expr()
                .and('t2.roster_team_id = m.team2_roster_id')
                .and('t2.event_id = d.event_id')
                .and('t2.division_id = d.division_id')
                .and('t2.deleted IS NULL')
                .and('t2.status_entry = 12'))
            .left_join('roster_team', 'tr', squel.expr()
                .and('tr.roster_team_id = m.ref_roster_id')
                .and('tr.event_id = d.event_id')
                .and('tr.division_id = d.division_id')
                .and('tr.deleted IS NULL')
                .and('tr.status_entry = 12'))
            .left_join(
                squel.rstr(`
                    LATERAL (
                        SELECT eos1.event_official_id
                        FROM event_official_schedule eos1
                        WHERE eos1.event_id = m.event_id
                            AND eos1.division_id = m.division_id
                            AND eos1.match_name = m.display_name
                            AND eos1.ref_num = 1
                        ORDER BY eos1.event_official_schedule_id DESC
                        LIMIT 1
                    )
                `),
                'eos',
                'TRUE'
            )
            .left_join('event_official', 'eof', 'eof.event_official_id = eos.event_official_id')
            .left_join('official', 'of', 'of.official_id = eof.official_id')
            .left_join(squel.rstr('"user"'), 'u', 'u.user_id = of.user_id')
            .where('e.event_id = ?', eventId)
            .where('m.team1_roster_id IS NOT NULL OR m.team2_roster_id IS NOT NULL')
            .order('m.secs_start')
            .limit(limit)
            .offset(offset);

        const { rows } = await Db.query(query);
        let totalCount = 0;
        rows.forEach(row => {
            if (!totalCount) {
                totalCount = parseInt(row.total_count, 10);
            }
            delete row.total_count;
        });

        return this._createPaginationResponse(rows, page, limit, totalCount);
    }

    /**
     * Get team roster data for a specific event with pagination
     * @param {number} eventId - The event ID
     * @param {number} page - Page number (1-based)
     * @param {number} limit - Items per page
     * @returns {Promise<Object>} Complete pagination response with data and metadata
     */
    async getPaginatedTeamRoster(eventId, page, limit) {
        const cacheKey = `volley-station:team-roster:${eventId}:${page}:${limit}`;

        return Cache.getResult(
            cacheKey,
            () => this._getPaginatedTeamRosterData(eventId, page, limit),
            {
                ttl: Cache.TTL_DEFAULT,
                tags: [
                    Cache.tag.dbTable('event', { event_id: eventId }),
                ],
            }
        );
    }

    /**
     * Get team roster data for a specific event with pagination (extracted for caching)
     * @private
     */
    async _getPaginatedTeamRosterData(eventId, page, limit) {
        const offset = (page - 1) * limit;

        const query = squel
            .select()
            .from('event', 'e')
            .field('d.division_id')
            .field('d.name', 'division_name')
            .field('rt.roster_team_id', 'team_id')
            .field('rt.team_name')
            .field('rc.club_name')
            .field(squel.rstr(`
                COALESCE((
                    SELECT jsonb_agg(jsonb_build_object(
                        'athlete_id', ra.roster_athlete_id,
                        'first', ma.first,
                        'last', ma.last,
                        'position', sp.short_name,
                        'jersey', CASE
                            WHEN e.sport_sanctioning_id = 1 THEN COALESCE(ra.aau_jersey, ma.aau_jersey)
                            ELSE COALESCE(ra.jersey, ma.jersey)
                        END
                    )
                    ORDER BY
                        CASE WHEN e.sport_sanctioning_id = 1 THEN COALESCE(ra.aau_jersey, ma.aau_jersey, 0)
                             ELSE COALESCE(ra.jersey, ma.jersey, 0) END,
                        ma.last, ma.first
                    )
                    FROM roster_athlete ra
                    JOIN master_athlete ma ON ma.master_athlete_id = ra.master_athlete_id
                        AND ma.deleted IS NULL
                    LEFT JOIN sport_position sp ON sp.sport_position_id = COALESCE(ra.sport_position_id, ma.sport_position_id)
                    WHERE ra.roster_team_id = rt.roster_team_id
                        AND ra.deleted IS NULL
                        AND ra.deleted_by_user IS NULL
                        AND (ra.as_staff = 0 OR ra.as_staff IS NULL)
                ), '[]'::jsonb)
            `), 'athletes')
            .field(squel.rstr(`
                COALESCE((
                    SELECT jsonb_agg(jsonb_build_object(
                        'first', s.first,
                        'last', s.last,
                        'role_name', s.role_name
                    ) ORDER BY s.sort_order NULLS LAST, s.role_name, s.last, s.first)
                    FROM (
                        SELECT ms.first, ms.last, r.name AS role_name, r.sort_order
                        FROM master_staff ms
                        JOIN roster_staff_role rsr ON rsr.master_staff_id = ms.master_staff_id
                            AND rsr.roster_team_id = rt.roster_team_id
                            AND rsr.deleted IS NULL
                            AND rsr.deleted_by_user IS NULL
                        LEFT JOIN master_staff_role msr ON msr.master_staff_id = rsr.master_staff_id
                            AND msr.master_team_id = rsr.master_team_id
                        LEFT JOIN role r ON r.role_id = COALESCE(NULLIF(rsr.role_id, '0'), msr.role_id)
                        UNION ALL
                        SELECT ma.first, ma.last, 'Staff' AS role_name, NULL::INTEGER AS sort_order
                        FROM master_athlete ma
                        JOIN roster_athlete ra ON ra.master_athlete_id = ma.master_athlete_id
                            AND ra.event_id = rt.event_id
                            AND ra.deleted IS NULL
                            AND ra.deleted_by_user IS NULL
                        WHERE ra.roster_team_id = rt.roster_team_id
                            AND ra.as_staff > 0
                    ) s
                ), '[]'::jsonb)
            `), 'staff')
            .field('COUNT(*) OVER()', 'total_count')
            .join('division', 'd', 'd.event_id = e.event_id')
            .join('roster_team', 'rt', squel.expr()
                .and('rt.event_id = d.event_id')
                .and('rt.division_id = d.division_id')
                .and('rt.deleted IS NULL')
                .and('rt.status_entry = 12'))
            .join('roster_club', 'rc', squel.expr()
                .and('rc.event_id = rt.event_id')
                .and('rc.roster_club_id = rt.roster_club_id')
                .and('rc.deleted IS NULL'))
            .where('e.event_id = ?', eventId)
            .order('rt.team_name')
            .order('rt.roster_team_id')
            .limit(limit)
            .offset(offset);

        const { rows } = await Db.query(query);

        let totalCount = 0;
        rows.forEach(row => {
            if (!totalCount) totalCount = parseInt(row.total_count, 10);
            delete row.total_count;
        });

        return this._createPaginationResponse(rows, page, limit, totalCount);
    }

    /**
     * Create a pagination response object
     * @param {Array} data - The data array
     * @param {number} page - Current page number
     * @param {number} limit - Items per page
     * @param {number} totalCount - Total number of items
     * @returns {Object} Pagination response
     */
    _createPaginationResponse(data, page, limit, totalCount) {
        const totalPages = Math.ceil(totalCount / limit);
        const hasNext = page * limit < totalCount;
        const hasPrev = page > 1;
        return {
            data,
            pagination: {
                page,
                limit,
                total: totalCount,
                totalPages,
                hasNext,
                hasPrev
            }
        };
    }
}

module.exports = new VolleyStationService();
