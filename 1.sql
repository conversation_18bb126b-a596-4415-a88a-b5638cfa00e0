EXPLAIN ANALYZE
SELECT
    e.event_id,
    d.gender,
    d.name AS division_name,
    d.division_id,
    m.match_id AS match_uuid,
    e.name AS EVENT,
    m.event_id || '_' || m.division_short_name || '_' || m.display_name AS match_id,
    m.division_short_name AS div,
    m.day, --
    to_char(m.secs_start, 'YYYY-MM-DD HH24:MI:SS') AS date_time,
    c.name AS court,
    c.short_name AS court_alpha,
    pb.display_name AS pool,
    m.team1_roster_id,
    m.team2_roster_id,
    m.ref_roster_id,
    t1.team_name AS team_1_name,
    t2.team_name AS team_2_name,
    tr.team_name AS ref_name,
    t1.master_team_id AS master_team_id_1,
    t2.master_team_id AS master_team_id_2,
    m.type AS match_type,
    (SELECT jsonb_object_agg(KEY, VALUE) FROM jsonb_each_text(m.results) WHERE KEY LIKE 'set%') AS results
FROM "event" AS e
    INNER JOIN "division" AS d
        ON d.event_id = e.event_id
    INNER JOIN  "matches" AS m
        ON m.event_id = d.event_id
        AND m.division_id = d.division_id
    LEFT JOIN "poolbrackets" AS pb
        ON pb.uuid = m.pool_bracket_id
    LEFT JOIN "courts" AS c
        ON c.uuid = m.court_id
    LEFT JOIN "roster_team" AS t1
        ON t1.roster_team_id = m.team1_roster_id
        AND t1.event_id = d.event_id
        AND t1.division_id = d.division_id
        AND t1.deleted IS NULL
        AND t1.status_entry = 12
    LEFT JOIN "roster_team" AS t2
        ON t2.roster_team_id = m.team2_roster_id
        AND t2.event_id = d.event_id
        AND t2.division_id = d.division_id
        AND t2.deleted IS NULL
        AND t2.status_entry = 12
    LEFT JOIN "roster_team" AS tr
        ON tr.roster_team_id = m.ref_roster_id
        AND tr.event_id = d.event_id
        AND tr.division_id = d.division_id
        AND tr.deleted IS NULL
        AND tr.status_entry = 12
WHERE
    e.event_id = 25184
  AND (t1.roster_team_id IS NOT NULL OR t2.roster_team_id IS NOT NULL)
ORDER BY m.secs_start
--     ASC LIMIT 3
