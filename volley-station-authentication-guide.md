# VolleyStation Authentication Implementation Guide

## Overview

This document provides detailed guidance for implementing token-based authentication for the VolleyStation integration, based on analysis of existing authentication patterns in the SportWrench codebase.

## Existing Authentication Patterns Analysis

### 1. Simple API Key Authentication (Recommended)

**Used by:** BallerTV, ACS integrations

**Pattern:**
```javascript
// api/policies/baller-tv/auth.js
module.exports = function (req, res, next) {
    const authorization = req.get('Authorization');
    if(!authorization) {
        res.status(401).json({ error: 'Authorization token not exists' });
        return;
    }
    
    if(authorization === sails.config.ballerTv.apiKey) {
        next();
    } else {
        res.status(401).json({ error: 'Authorization token invalid' });
    }
}
```

**Advantages:**
- Simple to implement and maintain
- Consistent with existing integrations
- Secure when properly configured
- No complex token management required

### 2. Hash-Based Authentication

**Used by:** THS, EventConnect, Housing integrations

**Pattern:**
```javascript
// api/policies/ths/auth.js
module.exports = function (req, res, next) {
    const authorization = req.get('Authorization');
    const authorizationHashParams = {
        url: req.url,
        timestamp: req.get('Timestamp'),
        consumerName: HOUSING_POLICY_PROVIDER.THS,
    }

    try {
        HousingService.verifyAuthValidation(authorization, authorizationHashParams);
        next();
    } catch (err) {
        return res.status(401).json(err);
    }
}
```

**Advantages:**
- More secure against replay attacks
- Includes timestamp validation
- URL-specific authentication

**Disadvantages:**
- More complex to implement
- Requires synchronized time between systems
- More difficult for third-party integration

### 3. OAuth Bearer Token

**Used by:** SportsEngine integration

**Pattern:**
```javascript
// api/services/SportsEngineService.js
async _prepareDataRequestOptions() {
    const headers = new fetch.Headers();
    headers.set('Authorization', `Bearer ${await this._getToken()}`);
    headers.set('Accept', 'application/json');
    return { headers };
}
```

**Use Case:** When integrating with external OAuth providers

## Recommended Implementation for VolleyStation

### Authentication Policy

**File:** `api/policies/volley-station/auth.js`

```javascript
module.exports = function (req, res, next) {
    const authorization = req.get('Authorization');
    
    if (!authorization) {
        return res.status(401).json({ 
            error: 'Authorization token required',
            message: 'Please provide Authorization header'
        });
    }

    if (authorization === sails.config.volleyStation.apiKey) {
        next();
    } else {
        return res.status(401).json({ 
            error: 'Invalid authorization token',
            message: 'The provided token is not valid'
        });
    }
};
```

### Configuration Setup

**File:** `config/volleyStation.js`

```javascript
module.exports.volleyStation = {
    apiKey: 'vs_dev_key_placeholder_replace_in_production'
};
```

**Environment-Specific Configuration:**

```javascript
// config/env/development.js
volleyStation: tryRequireConfig('volleyStation'),

// config/env/production.js  
volleyStation: tryRequireConfig('volleyStation'),
```

### Route Policy Application

**File:** `userconfig/routes/api/volley-station.js`

```javascript
module.exports = {
    // All VolleyStation endpoints require authentication
    'GET /api/volley-station/v1/events': {
        action: 'v2/API/volley-station/events',
        policy: 'volley-station/auth'
    },
    
    'GET /api/volley-station/v1/events/:eventId/schedule': {
        action: 'v2/API/volley-station/schedule',
        policy: 'volley-station/auth'
    },
    
    'GET /api/volley-station/v1/events/:eventId/athletes': {
        action: 'v2/API/volley-station/athletes',
        policy: 'volley-station/auth'
    },
    
    'GET /api/volley-station/v1/events/:eventId/staff': {
        action: 'v2/API/volley-station/staff',
        policy: 'volley-station/auth'
    },
    
    'POST /api/volley-station/v1/events/:eventId/results': {
        action: 'v2/API/volley-station/results',
        policy: 'volley-station/auth'
    }
};
```

## Event-Level Authorization

### Database Schema Reference

Events must have `enable_volley_station: true` in the `teams_settings` JSON column:

```sql
-- Example event configuration
UPDATE event 
SET teams_settings = jsonb_set(
    COALESCE(teams_settings, '{}'), 
    '{enable_volley_station}', 
    'true'
) 
WHERE event_id = 12345;
```

### Authorization Check Implementation

**Pattern used in controllers:**

```javascript
// In each VolleyStation controller action
const eventQuery = knex('event as e')
    .select('event_id', 'long_name')
    .where('event_id', eventId)
    .whereRaw(`(e.teams_settings->>'enable_volley_station')::BOOLEAN IS TRUE`)
    .whereRaw(`published IS TRUE`);

const { rows } = await Db.query(eventQuery);

if (!rows.length) {
    return exits.forbidden({
        error: 'Event not available for VolleyStation integration',
        message: 'This event is not configured for VolleyStation access'
    });
}
```

## Security Best Practices

### 1. API Key Management

**Development:**
- Use placeholder keys in configuration files
- Never commit real API keys to version control
- Use environment-specific configuration loading

**Production:**
- Store API keys in secure environment variables
- Rotate keys periodically
- Use strong, randomly generated keys (minimum 32 characters)

**Key Generation Example:**
```bash
# Generate secure API key
openssl rand -hex 32
# Result: vs_live_a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456
```

### 2. Request Validation

**Headers Validation:**
```javascript
// Validate required headers
const requiredHeaders = ['Authorization', 'Content-Type'];
const missingHeaders = requiredHeaders.filter(header => !req.get(header));

if (missingHeaders.length) {
    return res.status(400).json({
        error: 'Missing required headers',
        missing: missingHeaders
    });
}
```

### 3. Rate Limiting (Optional)

If high-volume usage is expected, consider implementing rate limiting:

```javascript
// Example rate limiting policy
module.exports = function (req, res, next) {
    // Implement rate limiting logic
    // Could use Redis to track request counts per API key
    next();
};
```

### 4. Audit Logging

**Log all API access:**
```javascript
// In authentication policy
module.exports = function (req, res, next) {
    const authorization = req.get('Authorization');
    
    // Log authentication attempt
    loggers.api_access.info('VolleyStation API access', {
        ip: req.getIP(),
        userAgent: req.getUserAgent(),
        url: req.url,
        method: req.method,
        timestamp: new Date().toISOString(),
        authorized: authorization === sails.config.volleyStation.apiKey
    });
    
    // ... rest of authentication logic
};
```

## Error Response Standards

### Authentication Errors

```javascript
// 401 - Missing token
{
    "error": "Authorization token required",
    "message": "Please provide Authorization header"
}

// 401 - Invalid token  
{
    "error": "Invalid authorization token",
    "message": "The provided token is not valid"
}

// 403 - Event not enabled
{
    "error": "Event not available for VolleyStation integration", 
    "message": "This event is not configured for VolleyStation access"
}
```

## Testing Authentication

### Unit Tests

```javascript
// Test authentication policy
describe('VolleyStation Authentication', () => {
    it('should reject requests without Authorization header', async () => {
        const response = await request(app)
            .get('/api/volley-station/v1/events')
            .expect(401);
            
        expect(response.body.error).toBe('Authorization token required');
    });
    
    it('should reject requests with invalid token', async () => {
        const response = await request(app)
            .get('/api/volley-station/v1/events')
            .set('Authorization', 'invalid_token')
            .expect(401);
            
        expect(response.body.error).toBe('Invalid authorization token');
    });
    
    it('should allow requests with valid token', async () => {
        const response = await request(app)
            .get('/api/volley-station/v1/events')
            .set('Authorization', sails.config.volleyStation.apiKey)
            .expect(200);
    });
});
```

### Integration Testing

```bash
# Test with curl
curl -H "Authorization: vs_dev_key_placeholder" \
     -H "Content-Type: application/json" \
     http://localhost:1337/api/volley-station/v1/events
```

## Deployment Considerations

### Environment Configuration

**Development:**
```javascript
// config/env/development.js
volleyStation: {
    apiKey: 'vs_dev_key_for_testing_only'
}
```

**Production:**
```javascript
// config/env/production.js
volleyStation: {
    apiKey: process.env.VOLLEY_STATION_API_KEY
}
```

### CI/CD Integration

Ensure API keys are properly configured in deployment pipelines:

```yaml
# .gitlab-ci.yml example
variables:
  VOLLEY_STATION_API_KEY: $VOLLEY_STATION_PROD_KEY
```

---

*This authentication approach follows SportWrench security patterns and provides a robust foundation for the VolleyStation integration.*
