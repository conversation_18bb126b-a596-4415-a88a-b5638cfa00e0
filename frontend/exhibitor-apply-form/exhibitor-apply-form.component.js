class ExhibitorApplyFormComponent {
    constructor(ExhibitorsService, ExhibitorsPaymentsService, EVENT_EXHIBITOR_MODAL_TABS, toastr) {
        this.ExhibitorsService = ExhibitorsService;
        this.ExhibitorsPaymentsService = ExhibitorsPaymentsService;
        this.tabs = EVENT_EXHIBITOR_MODAL_TABS;
        this.toastr = toastr;

        this.registrationInfo = {};
        this.saveInProcess = false;
        this.total = null;
        this.isFormSubmitted = false;

        this.EDIT_FIELDS = [
            'status',
            'is_sponsor',
            'is_exhibitor',
            'is_non_profit',
            'is_other'
        ];

        this.APPLY_FIELDS = [
            ...this.EDIT_FIELDS,
            'event_dates',
            'comment',
        ];
    }

    $onInit() {
        this.initDefaultTab();
    }

    get formattedRegistrationInfo() {
        if(!this.isApplyMode) {
            return _.pick(this.registrationInfo, this.EDIT_FIELDS);
        }

        const registrationInfo = _.pick(this.registrationInfo, this.APPLY_FIELDS);

        registrationInfo.booths = this.registrationInfo.selectedBooths.map(({ id }) => id);
        registrationInfo.otherBooths = this.ExhibitorsService.generateBoothsForRequest(this.registrationInfo);
        registrationInfo.amount = this.total;

        return registrationInfo;
    }

    initDefaultTab() {
        this.currentTab = this.tabs.APPLICATION_INFO;
    }

    onTabSelect(tab) {
        this.currentTab = tab;
    }

    loadTabContent(tab) {
        return this.currentTab === tab;
    }

    async saveApplication() {
        if (this.saveInProcess) {
            return;
        }

        this.saveInProcess = true;
        this.isFormSubmitted = true;

        try {
            this.validateRegistrationInfo();

            const registrationInfo = this.formattedRegistrationInfo;

            await this.onSaveExhibitor({
                eventID: this.eventId,
                exhibitorID: this.exhibitorId,
                data: registrationInfo,
            })

            this.toastr.success('Exhibitor profile successfully saved');
            this.onClose({ withReload: true });
        } catch (error) {
            if (error && error.validation) {
                this.toastr.error(error.validation);
            }
        } finally {
            this.saveInProcess = false;
        }
    }

    validateRegistrationInfo() {
        const registrationInfo = this.registrationInfo;

        if (!this.isApplyMode || _.isEmpty(registrationInfo)) {
            return;
        }

        if (this.ExhibitorsService.isPurchaseDataRequired(registrationInfo)) {
            this.ExhibitorsService.validateExhibitorPaymentData(registrationInfo);
        }
    }
}

angular.module('SportWrench').component('exhibitorApplyForm', {
    templateUrl: 'exhibitor-apply-form/exhibitor-apply-form.html',
    bindings: {
        eventId: '<',
        exhibitorId: '<',
        companyName: '<',
        isApplyMode: '<',
        onClose: '&',
        onSaveExhibitor: '&',
        isFormSubmitted: '<',
    },
    controller: [
        'ExhibitorsService',
        'ExhibitorsPaymentsService',
        'EVENT_EXHIBITOR_MODAL_TABS',
        'toastr',
        ExhibitorApplyFormComponent
    ]
});
