angular.module('SportWrench').factory('DateService', DateService);

DateService.$inject = ['moment'];

function DateService(moment) {
    var _normalizeDate = function (d) {
        var d_normalized = d.getTime() + d.getTimezoneOffset() * 60 * 1000;
        return new Date(d_normalized);
    }

    var _normalizeDateStr = function (date_str, skip) {
        if(date_str instanceof Date) return _normalizeDate(date_str);
        if(!skip && date_str && date_str.indexOf('+00') < 0) date_str += '+00';

        if (date_str) {
            return _normalizeDate(new Date(date_str))
        } else {
            return null;
        }

    }

    var _getNowNormalized = function () {
        return _normalizeDate(new Date());
    }

    var _parseUTC = function (serializedDate, format) {
        var parsedDate  = moment.utc(serializedDate, format).toDate(),
                utc     = parsedDate.getTime() + parsedDate.getTimezoneOffset() * 60 * 1000
        return moment(utc).toDate();
    }

    return {
        normalize: function (d) {
            return _normalizeDate(d);
        },
        normalizeStr: function (str, skip) {
            return _normalizeDateStr(str, skip);
        },
        nowNormalized: function () {
            return _getNowNormalized();
        },
        parseDate: function (dateStr, format) {
            return _parseUTC(dateStr, format)
        },
        parseUnix: function (unixDate) {
            return _parseUTC(parseInt(unixDate, 10))
        }
    }
}
