angular.module('SportWrench').factory('sportsService', sportsService);

sportsService.$inject = ['$http'];

function sportsService($http) {
    var getAllSports = function() {
        return $http.get('/api/sports');
    };
       
    var _get_sport_variation = function(params) {
        return $http.get('/api/sport_variation', {
            params: params
        });
    };

    var _get_sport_sanctioning = function(params) {
        return $http.get('/api/sport_sanctioning?', {
            params: params
        });
    };

    return  {
        getSports: function(callBack) {
            getAllSports().then(function (response) {
                callBack(response);
            });
        },        
        getSportVariations: function (params, callback) {
            _get_sport_variation(params).then(function (resp) {
                callback(resp);
            });
        },
        getSportSanctionings: function(params, callback) {
            _get_sport_sanctioning(params).then(function (resp) {
                callback(resp);
            });
        }
    };

}
