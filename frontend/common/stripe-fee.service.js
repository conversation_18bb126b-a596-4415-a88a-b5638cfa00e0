angular.module('SportWrench').service('StripeFeeService', ['UtilsService', 'FEE_PAYER', StripeFeeService]);

function StripeFeeService (UtilsService, FEE_PAYER) {
	this.utils 					= UtilsService;
	this.DEFAULT_STRIPE_FEE 	= 0.029;
	this.DEFAULT_STRIPE_FIXED 	= 0.3;
	this.DEFAULT_ACH_FEE 		= 0.008;
	this.ACH_FEE_LIMIT 			= 5;
	this.FEE_PAYER              = FEE_PAYER;
	this.MIN_CANCELLATION_AMOUNT= 0;
}

StripeFeeService.prototype.countCardFeeAmount = function (amount, eventPercent, eventTax, isDefault) {
	if(!amount) {
	    return 0;
    }

    let __percent   = parseFloat(eventPercent)  || this.DEFAULT_STRIPE_FEE;
    let __fixed     = parseFloat(eventTax)      || this.DEFAULT_STRIPE_FIXED;

	return isDefault
        ? this.__countDefaultStripeFee(amount, __percent, __fixed)
        : this.__countStripeFee(amount, __percent, __fixed);
};

StripeFeeService.prototype.__countStripeFee = function (amount, percent, fixed) {
    return this.utils.approxNumber((amount + fixed) / (1 - percent) - amount);
};

StripeFeeService.prototype.__countDefaultStripeFee = function (amount, percent, fixed) {
    return this.utils.approxNumber(amount * percent + fixed);
};

StripeFeeService.prototype.countACHFeeAmount = function (amount, eventFee, isDefault) {
	if(!amount) return 0;

	return isDefault
        ? this.__countDefaultACHFee(amount, eventFee)
        : this.__countCustomerACHFee(amount, eventFee);
};

StripeFeeService.prototype.__countCustomerACHFee = function (amount, percent) {
    return this.utils.approxNumber(amount / (1 - (percent || this.DEFAULT_ACH_FEE)) - amount);
}

StripeFeeService.prototype.__countDefaultACHFee = function (amount, percent) {
    return this.utils.approxNumber(amount * (parseFloat(percent) || this.DEFAULT_ACH_FEE));
}