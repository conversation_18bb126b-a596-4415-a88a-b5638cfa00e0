angular.module('SportWrench')

.factory('teamsService' , function($http) {
    var enteredTeam = function(roster_team_id, deleted) {
        return $http.post('/team/deleted', {
            roster_team_id: roster_team_id,
            deleted: deleted
        })
    };

    var _createMultipleMasterTeams = function(list) {
        return $http.post('/api/master_team/bulk', {
            teams: list
        });
    }

    return {
        updateMasterTeam: function(team, id) {
            return $http.put('/api/club/team/' + id + '/update', { team: team })
        },
        createMultipleMasterTeams: function(list, callBack) {
            _createMultipleMasterTeams(list).then(function(response) {
                callBack(response);
            })
        }        
    };
});
