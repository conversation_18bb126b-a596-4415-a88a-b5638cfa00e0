class Component {
    constructor(OfficialsPayoutsService, $stateParams, toastr, INTERNAL_ERROR_MSG, $rootScope, LOAD_PAYOUTS_ACTION) {
        this.service = OfficialsPayoutsService;
        this.eventID = $stateParams.event;
        this.toastr = toastr;
        this.$rootScope = $rootScope;
        this.INTERNAL_ERROR_MSG = INTERNAL_ERROR_MSG;
        this.LOAD_PAYOUTS_ACTION = LOAD_PAYOUTS_ACTION

        this.applyTo = '';

        this.onSaveError = '';

        this.loading = {
            setDefaultInProcess: false
        }
    }

    setDefault() {
        if (this.loading.setDefaultInProcess) {
            return;
        }

        this.onSaveError = '';

        const _categories = this.getCategories();

        if (!_categories.length && !this.applyTo) {
            return;
        }

        if (!_categories.length && this.applyTo) {
            this.onSaveError = 'Please, set an amount'

            return;
        }

        if (_categories.length && !this.applyTo) {
            this.onSaveError = 'Please, choose to whom to apply'

            return;
        }

        this.loading.setDefaultInProcess = true;

        this.service.setDefaultAdditionalPayments({
            eventID: this.eventID,
            categories: _categories,
            applyTo: this.applyTo,
            memberType: this.memberType
        }).then(() => {
            this.toastr.success('Default amounts set successfully');

            this.$rootScope.$broadcast(this.LOAD_PAYOUTS_ACTION);
        })
        .finally(() => {
            this.loading.setDefaultInProcess = false;
        })

    }

    getCategories() {
        return this.categories
            .filter(({ amount }) => amount)
            .map(({ amount, official_additional_payment_category_id}) => {
                return { 
                    amount: Number(amount), 
                    official_additional_payment_category_id 
                }
            })
    }

    showSaveButton() {
        return this.categories && this.categories.length;
    }
}

angular.module('SportWrench').component('setDefaultAdditionalPayments', {
    templateUrl: 'components/officials/officials-payouts/set-default-additional-payments/set-default-additional-payments.html',
    bindings: {
        onClose: '&',
        categories: '<',
        memberType: '<'
    },
    controller: [
        'OfficialsPayoutsService',
        '$stateParams',
        'toastr',
        'INTERNAL_ERROR_MSG',
        '$rootScope',
        'LOAD_PAYOUTS_ACTION',
        Component
    ],
});





