<form name="$ctrl.form" class="payout-form">
    <div class="row">
        <div class="col-sm-12">
            <div class="payout-form-row">
                <div ng-class="{'has-error': $ctrl.isInvalid('date_paid')}" class="input-group date-time-control payout-form_date-paid">
                    <input
                        name="date_paid"
                        type="text" 
                        class="te white-ro form-control pointer" 
                        datetime-picker="MM/dd/yyyy"
                        ng-click="$ctrl.toggleOpen()"
                        ng-model="$ctrl._info.date_paid"
                        is-open="$ctrl.datepickerSettings.isOpen"
                        datepicker-options="{showWeeks: false}"
                        enable-time="false"
                        required
                        readonly
                    >
                    <span class="input-group-addon">
                        <span class="glyphicon glyphicon-calendar"></span>
                    </span>
                </div>
                <div class="payout-form_payment-method">
                    <select 
                        ng-class="{'has-error': true}"
                        class="form-control" 
                        name="payment_method"
                        ng-model="$ctrl._info.payment_method" 
                        ng-options="e.id as e.label for e in $ctrl._info.payment_methods"
                        required
                    >
                    </select>
                </div>
                <div ng-class="{'has-error': $ctrl.isInvalid('amount')}" class="input-group payout-form_amount">
                    <span class="input-group-addon">$</span>
                    <input 
                        class="form-control"
                        name="amount"
                        ng-model="$ctrl._info.amount"
                        float-number
                        required
                    >
                </div>
                <div ng-if="$ctrl.showCheckNumber()" class="input-group payout-form_check-number">
                    <span class="input-group-addon">#</span>
                    <input 
                        class="form-control"
                        name="check_number"
                        ng-model="$ctrl._info.check_number"
                        placeholder="Check"
                    >
                </div>
            </div>
        </div>
        <div ng-if="$ctrl.showNotesField" class="col-sm-9">
            <textarea 
                class="form-control payout-form_notes"
                ng-model="$ctrl._info.notes"
                name="notes"
                rows="4"
                placeholder="Notes..."
                wrap="hard"></textarea>
        </div>
        <div class="col-sm-12">
            <div class="payout-form_footer-buttons">
                <div>
                    <button ng-click="$ctrl.toggleNotesField()" class="btn btn-primary">{{$ctrl.getNotesButtonName()}}</button>
                </div>
                <div class="to-right">
                    <button ng-click="$ctrl.initializeInfo()" class="btn btn-default">Cancel</button>
                    <button ng-click="$ctrl.markPayout()" class="btn btn-success">Save</button>
                </div>
            </div>
        </div>
        <div class="col-sm-12">
            <uib-alert ng-if="$ctrl.showAmountAlert()" type="danger row-space alert-sm">Amount exceeds the balance. Please double check.</uib-alert>
            <uib-alert ng-if="$ctrl.showPaymentMethodAlert()" type="danger row-space alert-sm">Selected payment method is not suitable for the official. Make sure they can accept it.</uib-alert>
        </div>
    </div>
</form>
