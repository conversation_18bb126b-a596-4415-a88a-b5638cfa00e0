<uib-accordion>

    <uib-accordion-group
        class="row-space payments-list--sm"
        ng-repeat="payment in payments"
        is-open="payment.is_open"
    >
        <uib-accordion-heading>

            <payment-info-accord-heading payment="payment" get-details="get_payment_details(payment)"></payment-info-accord-heading>

        </uib-accordion-heading>
        <!-- start content -->  
            <!-- Main content -->
            <div class="row" ng-if="payment.type === 'check' || !payment.show_partial_refund">
                <div class="col-sm-12"> 
                    <!-- Payment Info  -->                   
                    <div class="row">
                        <div class="col-xs-9">
                            <dl class="dl-horizontal payment-details">
                                <dt>Created:</dt>
                                <dd>{{payment.created}}</dd>

                                <dt ng-if="payment.payer">Payer:</dt>
                                <dd ng-if="payment.payer" ng-bind="payment.payer"></dd>

                                <dt ng-if="payment.email">Email:</dt>
                                <dd ng-if="payment.email" ng-bind="payment.email"></dd>       

                                <dt ng-if="payment.stripe_charge_id" >Stripe charge:</dt>
                                <dd ng-if="payment.stripe_charge_id" ng-bind="payment.stripe_charge_id"></dd>       

                                <dt ng-if="payment.card_name">Card name:</dt>
                                <dd ng-if="payment.card_name" ng-bind="payment.card_name"></dd>     

                                <dt ng-if="payment.card_last_4">Card last 4:</dt>
                                <dd ng-if="payment.card_last_4" ng-bind="payment.card_last_4"></dd>     

                                <dt>Invoice:</dt>
                                <dd>
                                   <a href="" ng-click="openReceipt(payment.purchase_id)" target="_blank">#{{payment.purchase_id}}</a>
                                </dd>

                                <dt ng-if="payment.dispute_status">Dispute:</dt>
                                <dd ng-if="payment.dispute_status">
                                    <span ng-bind="payment.dispute_status" uib-tooltip="Created: {{payment.dispute_created}}"></span>
                                </dd>
                            </dl>
                        </div>
                        <div class="col-xs-3">
                            <button  
                                class="btn btn-danger pull-right"
                                ng-if="payment.type === 'check' && !payment.canceled_date"
                                sw-confirm="Do you really want to cancel this payment?"
                                sw-confirm-do="cancel"
                                sw-confirm-args="payment"
                                sw-confirm-params="confirmParams"
                                sw-confirm-hide-no>Void Invoice</button>
                            <div class="btn-group" uib-dropdown is-open="status.isopen" ng-if="showRefundButton(payment)">
                                <button ng-disabled="isRefundPending" type="button" class="btn btn-danger pull-right" uib-dropdown-toggle>
                                    <spinner is-inline="true" size="1" no-wrapper="true" active="isRefundPending"></spinner>
                                    Refund
                                    <span class="caret"></span>
                                </button>
                                <ul class="dropdown-menu" role="menu">
                                    <li><a href="" ng-click="partialRefundMenu(payment)">Partial</a></li>
                                    <li>
                                        <a
                                            href="" 
                                            sw-confirm="Do you really want to refund this payment?"
                                            sw-confirm-do="refund"
                                            sw-confirm-args="payment"
                                            sw-confirm-params="confirmParams"
                                            sw-confirm-hide-no>Total</a>
                                    </li>
                                </ul>
                            </div>
                        </div>                            
                            
                        </div>
                    </div>

                    <div class="col-xs-12" ng-if="payment.refunds.length">
                        <dl class="dl-horizontal payment-details">
                            <dt>Refunds:</dt>
                            <dd>
                                <team-refunds-list payment="payment"></team-refunds-list>
                            </dd>
                        </dl>
                    </div>

                    <receive-check-form
                        ng-if="showReceiveCheckForm(payment)"
                        payment="payment"
                        on-save="receiveCheck(data, payment.purchase_id)"
                    ></receive-check-form>
        
                    <payment-note
                        save-note="save_note(note, payment.purchase_id)"
                        payment-note="payment.notes"
                    ></payment-note>
                    
                    <payment-items-list
                        items="payment.teams"
                        payment-type="payment.type"
                        payment-status="payment.status"
                        is-canceled="!!payment.canceled_date"
                        on-cancel="cancelTeam(item, payment)"
                        on-team-change="changeTeam(payment)">
                    </payment-items-list>

                </div>
            </div> 
            <!-- Partial refund menu --> 
            <div class="row" ng-if="payment.show_partial_refund">
                <div class="col-sm-12">
                    <a href="" ng-click="hide_partial_refund_menu(payment)"><i class="fa fa-angle-double-left"></i> back to payment details</a>
                    <partial-refund                        
                        payment="payment"
                        teams="payment.teams"
                        save="save_partial_refund(payment, refunded_teams)"
                    ></partial-refund>
                </div>
            </div>
        <!-- end content -->
    </uib-accordion-group>
</uib-accordion>
