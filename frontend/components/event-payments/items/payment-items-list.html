<table class="table table-condensed">
    <thead>
        <tr>
            <th>Amount</th>
            <th>Discount</th>
            <th>Division</th>
            <th>Team name</th>
            <th>Status</th>
        </tr>
    </thead>
    <tbody>
        <tr 
        	ng-repeat="item in $ctrl.items" 
        	ng-class="$ctrl.rowCls(item)">

        	<td ng-bind="item.amount | currency"></td>
        	<td ng-bind="(item.discount | currency) || '-'"></td>
        	<td ng-bind="item.division_name"></td>
        	<td>

                <span ng-if="!$ctrl.showChangeTeamPicker(item)" ng-bind="item.team_name"></span>

                <change-team-picker
                    ng-if="$ctrl.showChangeTeamPicker(item)"
                    team-name="item.team_name"
                    paid-amount="item.amount"
                    discount="item.discount"
                    payment-type="$ctrl.type"
                    replace-team="$ctrl.replaceTeam(item.purchase_team_id, item.roster_team_id, team_id, name, discount_diff, reg_fee)"
                ></change-team-picker>  

            </td>
            <td>
                <status-paid status="{{item.status_paid}}" ng-if="!item.canceled"></status-paid>
                <span ng-if="item.canceled">Canceled</span>
                
                <small class="canellation-link">
                    &nbsp;
                    <a 
                        href=""
                        ng-if="$ctrl.showVoidBtn(item)"
                        sw-confirm="{{$ctrl.voidConfirmText(item)}}" 
                        sw-confirm-do="$ctrl.cancelItem" 
                        sw-confirm-hide-no
                        sw-confirm-args="item">
                        {{$ctrl.voidBtnText()}}
                    </a>
                </small>
            </td>

        </tr>
    </tbody>
</table>
