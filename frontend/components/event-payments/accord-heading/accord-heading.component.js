angular.module('SportWrench').component('paymentInfoAccordHeading', {
	templateUrl : 'components/event-payments/accord-heading/accord-heading.html',
	bindings 	: {
		p 			: '<payment',
		getDetails 	: '&'
	},
	controller: PaymentInfoAccordHeadingController
});

PaymentInfoAccordHeadingController.$inject = ['PAYMENT_STATUS', 'PAYMENT_TYPE'];

function PaymentInfoAccordHeadingController (PAYMENT_STATUS, PAYMENT_TYPE) {

	this.headingClass = function () {
		let payment = this.p;

		if (payment) {
			if (payment.canceled_date || (payment.status === PAYMENT_STATUS.CANCELED)) {
				return 'text-grayout';
			} else if (payment.status === PAYMENT_STATUS.PAID) {
				return 'text-success';
			}
		}

        return 'text-info';
    };

    this.getPaymentStatus = function () {
        if(this.p.has_not_won_dispute) {
            return `dispute: ${this.p.dispute_status}`;
        } else {
            return this.p.status;
        }
    };

    this.isStripe = function () {
    	return (this.p.type === PAYMENT_TYPE.CARD) || (this.p.type === PAYMENT_TYPE.ACH);
    };

    this.showCheckRefundBlock = function () {
    	return this.p.date_refunded && !this.p.canceled_date
    }
}
