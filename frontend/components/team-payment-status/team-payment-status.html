<div class="row team-payment-status">
    <div class="col-xs-12">
        <div class="row">
            <div class="text-center">
                <br/>
                <label class="radio-inline {{$ctrl.disableChange ? 'disabled' : ''}}">
                    <input type="radio"
                           value="{{$ctrl.STATUS.PAID}}"
                           name="paymentStatus"
                           ng-model="$ctrl.paymentStatus"
                           ng-disabled="$ctrl.disableChange"
                    >
                    <strong>Paid</strong>
                </label>
                <label class="radio-inline {{$ctrl.disableChange ? 'disabled' : ''}}">
                    <input type="radio"
                           value="{{$ctrl.STATUS.NONE}}"
                           name="paymentStatus"
                           ng-model="$ctrl.paymentStatus"
                           ng-disabled="$ctrl.disableChange"
                    >
                    <strong>Not Paid</strong>
                </label>
                <label class="radio-inline disabled">
                    <input type="radio"
                           value="{{$ctrl.STATUS.PENDING}}"
                           name="paymentStatus"
                           ng-model="$ctrl.paymentStatus"
                           ng-disabled="true"
                    >
                    <strong>Pending</strong>
                </label>
                <label class="radio-inline disabled">
                    <input type="radio"
                           value="{{$ctrl.STATUS.DISPUTED}}"
                           name="paymentStatus"
                           ng-model="$ctrl.paymentStatus"
                           ng-disabled="true"
                    >
                    <strong>Disputed</strong>
                </label>
                <label class="radio-inline disabled">
                    <input type="radio"
                           value="{{$ctrl.STATUS.REFUNDED}}"
                           name="paymentStatus"
                           ng-model="$ctrl.paymentStatus"
                           ng-disabled="true"
                    >
                    <strong>Refunded</strong>
                </label>
            </div>
        </div>
        <div class="row row-space selected-row" ng-class="{ 'show-row': $ctrl.statusChanged() }">
            <div class="col-sm-10">
                <input type="text" class="form-control" ng-model="$ctrl.notes" placeholder="Enter notes for this change">
            </div>
            <div class="col-sm-2 text-right">
                <button  class="btn btn-primary" style="width:100%" ng-click="$ctrl.saveChanges()">Save</button>
            </div>
        </div>
    </div>
</div>
