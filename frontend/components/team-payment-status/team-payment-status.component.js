

class Controller {
    constructor (TEAM_STATUS, AllTeamsService) {
        this.STATUS = TEAM_STATUS.PAYMENT;
        this.AllTeamsService = AllTeamsService;
    }

    $onInit () {
        this.changed = false;
        this.notes = '';
        this.initialValue = this.paymentStatus;

        this.disableChange = ![this.STATUS.PAID, this.STATUS.NONE].includes(Number(this.paymentStatus));
    }

    statusChanged () {
        return Number(this.initialValue) !== Number(this.paymentStatus);
    }

    saveChanges () {
        this.statusPaidChanged({status: Number(this.paymentStatus), notes: this.notes})
            .then(() => this.initialValue = this.paymentStatus);
    }
}

Controller.$inject = ['TEAM_STATUS', 'AllTeamsService'];

angular.module('SportWrench').component('teamPaymentStatus', {
    templateUrl: 'components/team-payment-status/team-payment-status.html',
    bindings: {
        paymentStatus: '<',
        statusPaidChanged: '&'
    },
    controller: Controller
});
