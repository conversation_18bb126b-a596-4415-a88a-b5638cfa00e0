angular.module('SportWrench').directive('genders', genders);

genders.$inject = ['_'];

function genders(_) {
    return {
        restrict: 'E',
        scope: {
            f: '<',
            m: '<',
            nb: '<',
            w: '<',
            h: '<',
            title_f: '<',
            title_m: '<',
            fw: '<',
        },
        templateUrl: 'components/genders/genders.html',
        link: function (scope, attrs, elem) {
            _.each(['f', 'm', 'fw', 'nb', 'w', 'h'], (e)=> {
                if(!elem[e] && typeof elem[e] == 'string') scope[e]= true;
            })
        }
    }
}
