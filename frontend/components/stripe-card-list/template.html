<p class="stripe-form-label">Saved Payment Methods</p>
<ul class="stripe-card-list">
    <li class="stripe-card"
        ng-class="{'stripe-card-selected': $ctrl.cardSelected(c)}"
        ng-repeat="c in $ctrl.filteredCards"
        ng-click="$ctrl.selectCard(c)"
    >
        <div>{{$ctrl.getLabel(c)}}</div>
        <div>****{{$ctrl.getLast4(c)}}</div>
    </li>
    <li class="stripe-card"
        ng-click="$ctrl.resetCard()"
        ng-class="{'stripe-card-selected': $ctrl.selectedCard === null}"
    >Use a Different Method</li>
</ul>
