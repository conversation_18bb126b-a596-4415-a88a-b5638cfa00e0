angular.module('SportWrench').component('official', {
    templateUrl: 'components/members-info/official/official.html',
    bindings: {
        eventId             : '<',
        officialId          : '<',
        onOfficialChanged   : '&',
        isOfficialChanged   : '=',
        onClose             : '&',
        loadingModalTitle   : '&',
        resetUnsavedChanges : '&',
        setModalTitle       : '&',
        childIsLoaded       : '&',
        getMemberStatuses   : '&',
        memberOfficial      : '=official',
        operationService    : '<',
    },
    controller: [
        'ConfirmationService',
        'toastr',
        '$rootScope',
        '$window',
        'officialService',
        Component
    ],
});

function Component (
    ConfirmationService,
    toastr,
    $rootScope,
    $window,
    officialService
) {
    const self = this;
    
    this.WORK_STATUSES = {
        APPROVED: 'approved',
    };

    this.INTERNAL_SERVER_ERROR = 'Internal Server Error.';

    this.loading = {
        success         : false,
        error           : false,
        errorMessage    : '',
    };

    this.disableMakeRemoveHeadBtn = false;
    this.disableShowQRCodeBtn = false;
    this.officialAdditionalRoles = [];

    this.$onInit = () => {
        this.loadingModalTitle();
        this.resetUnsavedChanges();

        this.service = this.operationService || officialService;

        this.service.getOfficial(this.eventId, this.officialId)
            .then(this.onGetOfficialSuccess, this.onGetOfficialError)
            .finally(this.childIsLoaded);
        
        officialService.loadOfficialAdditionalRoles()
            .then(function (resp) {
                self.officialAdditionalRoles  = resp.data;
            });
    };

    this.onGetOfficialSuccess = ({ data }) => {
        this.loading.success = true;

        let { event, official, clothes, email_contact_provider } = data;

        this.event                  = event;
        this.official               = official;
        this.disableShowQRCodeBtn   = !official.checkin_barcode;
        this.clothes                = clothes;
        this.emailContactProvider   = email_contact_provider;
        this.memberOfficial         = official;

        this.initOfficial = angular.copy(official, this.initOfficial);

        this.setModalTitle({ member: official, isHead: official.head_official });
        this.getMemberStatuses({ member: official });
    };

    this.onGetOfficialError = (error) => {
        this.loading.success    = false;
        this.loading.error      = true;

        this.loading.errorMessage = (error.data && error.data.validation)
            ? error.data.validation
            : this.INTERNAL_SERVER_ERROR
    };

    this.updateOfficial = (data) => {
        return this.service.updateOfficial(this.eventId, this.officialId, data);
    };

    this.showEntryQRCodeBtn = () => {
        const rules = [
            this.event.official_qr_code_enable,
            this.official.work_status === this.WORK_STATUSES.APPROVED,
        ]

        return rules.every(rule => rule);
    }

    this.showSendQRBtn = () => {
        const rules = [
            this.event.official_qr_code_enable,
            this.official.work_status === this.WORK_STATUSES.APPROVED,
        ]

        return rules.every(rule => rule);
    }

    this.showMakeHeadBtn = () => {
        const { head_official, deleted, work_status } = this.official;

        const rules = [
            !head_official,
            !deleted,
            work_status === this.WORK_STATUSES.APPROVED,
            _.isFunction(this.service.makeHead)
        ];

        return rules.every(rule => rule);
    };

    this.showRemoveHeadBtn = () => {
        const { head_official, deleted, work_status } = this.official;

        const rules = [
            head_official,
            !deleted,
            work_status === this.WORK_STATUSES.APPROVED,
            _.isFunction(this.service.removeHead)
        ];

        return rules.every(rule => rule);
    };

    this.changesListener = function (isChanged) {
        this.onOfficialChanged({ isChanged: isChanged });

        this.isOfficialChanged = this.getOfficialChangeStatus();
    };

    this.makeHead = () => {
        return this.service.makeHead(this.eventId, this.officialId)
            .then(response => {
                this.official.head_official = true;

                return response;
            })
    };

    this.removeHead = () => {
        return this.service.removeHead(this.eventId, this.officialId)
            .then(response => {
                this.official.head_official = false;

                return response;
            });
    };

    this.onConfirmationError = (error) => {
        this.loading.error = true;
        this.loading.errorMessage = (error.data && error.data.validation)
            ? error.data.validation
            : this.INTERNAL_SERVER_ERROR
    };

    this.onConfirmationMakeHeadSuccess = (answer) => {
        if (answer === ConfirmationService.YES_RESP) {
            return this.makeHead()
                .then(response => {
                    this.setModalTitle({ member: this.official, isHead: true});

                    const { first, last } = this.official;

                    toastr.success(
                        `${first} ${last} has been successfully made a Head Official.`
                    );

                    this.official.is_email_contact_provider = response.is_email_contact_provider;
                    this.official.is_email_notifications_receiver = response.is_email_notifications_receiver;

                    this.isOfficialChanged = this.getOfficialChangeStatus();
                })
        }
    };

    this.onMakeHead = () => {
        const { first, last } = this.official;

        const askMessage    = `Do you really want to make ${first} ${last} a Head Official?`;
        const title         = 'Make Head Official';

        ConfirmationService.ask(askMessage, {
            disableNoBtn: true,
            title: title,
        }).then(this.onConfirmationMakeHeadSuccess, this.onConfirmationError);
    };

    this.openEntryQRCodePage = () => {
        const { entry_qr_url } = this.official;

        if(!this.disableShowQRCodeBtn) {
            $window.open(entry_qr_url, '_blank');
        }
    }

    this.onSendQR = () => {
        const { first, last } = this.official;

        const askMessage    = `Do you really want to generate and send QR Code to ${first} ${last}?`;
        const title         = 'Send QR Code';

        ConfirmationService.ask(askMessage, { 
            disableNoBtn: true, 
            title
        }).then(this.onConfirmationSendQR, this.onConfirmationError)
    }

    this.onConfirmationSendQR = (answer) => {
        if (answer === ConfirmationService.YES_RESP) {
            const data = { selectedIDs: [this.officialId] };

            return officialService.sendEntryQRCodes(this.eventId, data)
                .then(() => {
                    toastr.success('QR Codes sent');
                });
        }
    }

    this.onConfirmationRemoveHeadSuccess = (answer) => {
        if (answer === ConfirmationService.YES_RESP) {
            return this.removeHead()
                .then(response => {
                    this.setModalTitle({ member: this.official, isHead: false});

                    const { first, last } = this.official;

                    toastr.success(
                        `${first} ${last} has been successfully removed from being a Head Official.`
                    );

                    this.official.is_email_contact_provider = response.is_email_contact_provider;
                    this.official.is_email_notifications_receiver = response.is_email_notifications_receiver;
                    this.resetEmailContactProvider(this.officialId);

                    this.isOfficialChanged = this.getOfficialChangeStatus();
                })
        }
    };

    this.onRemoveHead = () => {
        const { first, last } = this.official;

        const askMessage = `Do you really want to remove ${first} ${last} from Head Official?`;
        const title = 'Remove Head Official';

        ConfirmationService.ask(askMessage, {
            disableNoBtn: true,
            title: title,
        }).then(this.onConfirmationRemoveHeadSuccess, this.onConfirmationError)

    };

    this.resetEmailContactProvider = (officialId) => {
        if (officialId === this.emailContactProvider.id) {
            this.emailContactProvider.id = 0;
            this.emailContactProvider.first = null;
            this.emailContactProvider.last = null;
        }
    };

    this.getOfficialChangeStatus = () => {
        return !_.isEqual(this.official, this.initOfficial);
    };

    $rootScope.$on('official-data-not-saved', () => {
        this.disableMakeRemoveHeadBtn = true;
    });

    $rootScope.$on('official-data-saved', () => {
        this.disableMakeRemoveHeadBtn = false;

        if (this.official.work_status !== this.WORK_STATUSES.APPROVED) {
            this.official.head_official = false;
            this.resetEmailContactProvider(this.officialId);
        }

        this.setModalTitle({ member: this.official, isHead: this.official.head_official });
    });
}
