angular.module('SportWrench').component('pagination', {
    templateUrl 	: 'components/pagination/pagination.html',
    bindings 		: {
        page        : '<',
        limit       : '<',
        total       : '<',
        current     : '<',
        loading     : '<',
        changePage  : '&'
    },
    controller  : PaginationController
});

function PaginationController () {
    let self = this;

    this.paginationText = function () {
        let from = (self.page - 1) * self.limit;
        return `${from + 1} - ${from + self.current} of ${self.total}`
    };

    this.decrementPage = function (toFirst) {
        if(self.page <= 1 || self.loading) {
            return;
        }

        if(toFirst) {
            self.page = 1;
        } else {
            self.page--;
        }

        this.changePage({page: self.page});
    };

    this.incrementPage = function (toLast) {
        if((self.limit > self.current) || self.loading) {
            return;
        }

        if(toLast) {
            self.page = Math.ceil(self.total / self.limit);
        } else {
            self.page++;
        }

        self.changePage({page: self.page});
    };

    this.isToFirstPageButtonDisabled = function () {
        return self.page === 1 || (self.page !== 1 && self.loading);
    };

    this.isDecrementPageDisabled = function () {
        return self.page === 1 || self.loading;
    };

    this.isIncrementPageDisabled = function () {
        return (self.page * self.limit) >= self.total || self.loading
    };

    this.isToLastPageButtonDisabled = function () {
        return self.isIncrementPageDisabled();
    };
}
