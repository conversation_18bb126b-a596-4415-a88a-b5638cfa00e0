<div ng-class="{
    'form-group': true,
    'custom-form-validation-required': $ctrl.field.is_required,
    'has-error': $ctrl.fieldHasError({field: $ctrl.field}) }">
    <label class="col-sm-4 control-label" ng-bind="$ctrl.field.label"></label>
    <div class="col-sm-7">
        <input ng-if="$ctrl.field.variation === 'default'"
               type="text"
               ng-model="$ctrl.value"
               name="{{$ctrl.field.name}}"
               class="form-control"
               ng-required="$ctrl.field.is_required"
               aria-describedby="textHelpBlock"/>
        <input ng-if="$ctrl.field.variation === 'phone'"
               ng-model="$ctrl.value"
               name="{{$ctrl.field.name}}"
               type="tel"
               ui-mask="(*************"
               phone-validator
               class="form-control"
               ng-required="f$ctrl.field.is_required"
               aria-describedby="textHelpBlock"/>
        <input ng-if="$ctrl.field.variation === 'email'"
               type="email"
               email-validator
               ng-model="$ctrl.value"
               name="{{$ctrl.field.name}}"
               class="form-control"
               ng-required="$ctrl.field.is_required"
               aria-describedby="textHelpBlock"/>
        <input ng-if="$ctrl.field.variation === 'url'"
               type="url"
               class="form-control"
               placeholder="http://website.com/"
               ng-model="$ctrl.value"
               name="{{$ctrl.field.name}}"
               ng-required="$ctrl.field.is_required"
               aria-describedby="textHelpBlock"/>
        <span id="textHelpBlock"
              class="help-block"
              ng-show="$ctrl.field.help_text"
              ng-bind="$ctrl.field.help_text"></span>
    </div>
</div>
