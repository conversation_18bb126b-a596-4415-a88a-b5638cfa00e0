<div ng-class="{
    'form-group': true,
    'custom-form-validation-required': $ctrl.field.is_required,
    'has-error': $ctrl.fieldHasError({field: $ctrl.field}) }">
    <label class="col-sm-4 control-label" ng-bind="$ctrl.field.label"></label>
    <div class="col-xs-7">
        <div class="input-group date-time-control">
            <input type="text"
                   ng-model="$ctrl.date"
                   name="{{$ctrl.field.name}}"
                   class="form-control white-ro pointer"
                   uib-datepicker-popup="MM/dd/yyyy"
                   is-open="$ctrl.utils.isOpen"
                   ng-change="$ctrl.onDateChanged()"
                   ng-required="$ctrl.field.is_required"
                   placeholder="MM/dd/yyyy"
                   aria-describedby="dateHelpBlock"
                   ng-click="$ctrl.toggleOpen()"
                   readonly>
            <span class="input-group-addon">
                <span class="glyphicon glyphicon-calendar" ng-click="$ctrl.toggleOpen()"></span>
            </span>
        </div>
        <span id="dateHelpBlock"
              class="help-block"
              style="font-weight: normal"
              ng-show="$ctrl.field.help_text"
              ng-bind="$ctrl.field.help_text"></span>
    </div>
</div>
