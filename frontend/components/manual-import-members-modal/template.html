<div class="modal-header">
    <h3 class="text-info">Add file with Email Receivers</h3>
</div>
<div class="modal-body form-group">
    <form novalidate name="$ctrl.form" ng-submit="$ctrl.save()" id="manual-import-members-form">
        <div
            class="form-group"
            ng-class="{'has-error': $ctrl.form.$submitted && $ctrl.form.file.$invalid }"
        >
            <input type="file"
                   id="file"
                   name="file"
                   ng-model="$ctrl.file"
                   file-change="$ctrl.onChanges()"
                   required
            > Upload file (XLSX, CSV)
            <br/>
            <a href="/data/import/members_import_demo.xlsx">Download a sample</a>
        </div>

        <div class="row-space"></div>
        <uib-alert type="danger" ng-if="$ctrl.isErrorVisible()">{{ $ctrl.error }}</uib-alert>
    </form>
</div>
<div class="modal-footer">
    <button class="btn btn-success"
            type="submit"
            form="manual-import-members-form"
            ng-disabled="$ctrl.isSubmitDisabled()"
    >Add</button>
    <button class="btn btn-default" ng-click="$ctrl.close()">Close</button>
</div>
