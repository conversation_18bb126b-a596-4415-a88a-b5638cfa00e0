

class Controller {
    constructor(
        EventCustomPaymentService, $stateParams, UtilsService, StripeFeeService, MIN_PAYMENT_INTENT_AMOUNT, $scope,
        toastr, APP_ROUTES, $state, PAYMENT_INTENT_STATUS, PaymentCardService, STRIPE_PAYMENT_TYPE, MIN_AMOUNT_FOR_ACH
    ) {
        this._EventCustomPaymentService = EventCustomPaymentService;
        this._$stateParams = $stateParams;
        this._UtilsService = UtilsService;
        this._StripeFeeService = StripeFeeService;
        this._PaymentCardService = PaymentCardService;
        this.STRIPE_PAYMENT_TYPE = STRIPE_PAYMENT_TYPE;
        this.MIN_AMOUNT_FOR_ACH = MIN_AMOUNT_FOR_ACH;
        this._toastr = toastr;

        this._$scope = $scope;
        this._$state = $state;

        this.MIN_PAYMENT_INTENT_AMOUNT = MIN_PAYMENT_INTENT_AMOUNT;
        this.PAYMENT_INTENT_STATUS = PAYMENT_INTENT_STATUS;

        this.APP_ROUTES = APP_ROUTES;
    }

    $onInit () {
        this.eventPaymentCard = {};
        this.uncollectedSWFee = Math.abs(this.uncollectedFee);
        this.paymentTypeTitle = this._getPaymentTypeTitle();

        this._$scope.modalTitle = '<h4>Make Payment</h4>';

        this.showIncompletePaymentAlert = false;

        this.amount = 0;
        this.merchantFee = 0;
        this.total = 0;

        this.amountErrorMessage = '';

        this._getEventPaymentType();
    }

    goToIncompletePayments () {
        this._$state.go(this.APP_ROUTES.EO.PAYMENT_CARD, { active_tab: 2 });
    }

    validateACHAmount(amount) {
        if(amount < this.MIN_AMOUNT_FOR_ACH) {
            this.amountErrorMessage = `Amount should be greater than $${this.MIN_AMOUNT_FOR_ACH}`
        }
    }

    validateCardAmount(amount) {
        if(amount < this.MIN_PAYMENT_INTENT_AMOUNT) {
            this.amountErrorMessage = `Amount should be greater than $${this.MIN_PAYMENT_INTENT_AMOUNT}`;
        }
    }

    getMinPaymentAmount() {
        switch (this.eventPaymentCard.type) {
            case this.STRIPE_PAYMENT_TYPE.CARD:
                return this.MIN_PAYMENT_INTENT_AMOUNT;
            case this.STRIPE_PAYMENT_TYPE.ACH:
                return this.MIN_AMOUNT_FOR_ACH;
            default:
                throw new Error('Invalid payment method')
        }
    }

    amountChanged () {
        this.amountErrorMessage = '';

        let amount = Number(this.amount);

        if(!_.isUndefined(amount)) {
            if(amount > this.uncollectedSWFee) {
                this.amountErrorMessage = `Amount should be less uncollected SW Fee`;
            }

            if(amount < this.getMinPaymentAmount()) {
                this.amountErrorMessage = `Amount should be greater than $${this.getMinPaymentAmount()}`;
            }
        }

        this.merchantFee = this.getMerchantFee();
        this.total = this.getTotal();
    }

    getMerchantFee () {
        if(!this.amount) {
            return 0;
        }

        return this._StripeFeeService.countCardFeeAmount(Number(this.amount));
    }

    getTotal () {
        if(!this.amount) {
            return 0;
        }

        return this._UtilsService.approxNumber(Number(this.amount) + this.merchantFee);
    }

    cardLabel () {
        return this._PaymentCardService.getCardLabel(this.eventPaymentCard);
    }

    pay () {
        if(this.amountErrorMessage || !Number(this.amount)) {
            return;
        }

        this.paymentIsInProgress = true

        let payment = {
            amount: this.amount,
            merchant_fee: this.merchantFee,
            total: this.total,
            payment_for: 'uncollected_fee',
            payment_for_type: this.paymentType,
            description: this.description
        }

        return this._EventCustomPaymentService.createPayment(this._$stateParams.event, payment)
            .then(response => {
                let status = response && response.status;

                if(status === this.PAYMENT_INTENT_STATUS.REQUIRES_PAYMENT_METHOD) {
                    this._toastr.warning('Payment requires authentication');

                    this.showIncompletePaymentAlert = true;
                } else {
                    this._toastr.success('Payment Created');

                    this.onSave();
                }
            })
            .catch(() => {
                this._toastr.error('Payment Failed');
            })
            .finally(() => this.paymentIsInProgress = false)
    }

    payButtonDisabled () {
        return this.paymentIsInProgress ||
            !!this.amountErrorMessage ||
            this.showIncompletePaymentAlert ||
            (!Number(this.amount));
    }

    closeModal () {
        if(this.showIncompletePaymentAlert) {
            this.onSave();
        } else {
            this.close();
        }
    }

    _getEventPaymentType () {
        return this._EventCustomPaymentService.getEventMethodDetails(this._$stateParams.event)
            .then(response => {
                if(response && response.details) {
                    this.eventPaymentCard = response.details;
                }
            })
    }

    _getPaymentTypeTitle () {
        return `SW Fee for ${this._UtilsService.capitalizeFirstLetter(this.paymentType)}`;
    }
}

Controller.$inject = [
    'EventCustomPaymentService', '$stateParams', 'UtilsService', 'StripeFeeService', 'MIN_PAYMENT_INTENT_AMOUNT',
    '$scope', 'toastr', 'APP_ROUTES', '$state', 'PAYMENT_INTENT_STATUS', 'PaymentCardService', 'STRIPE_PAYMENT_TYPE', 'MIN_AMOUNT_FOR_ACH'
];

angular.module('SportWrench').component('customPaymentForm', {
    templateUrl: 'components/custom-payment-form/template.html',
    bindings: {
        paymentType: '<',
        uncollectedFee: '<',
        close: '&',
        onSave: '&'
    },
    controller: Controller
});
