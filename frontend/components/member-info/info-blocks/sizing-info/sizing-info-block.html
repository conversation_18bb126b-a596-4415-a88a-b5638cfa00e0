<div class="member-info_block-wrapper">
    <div class="row">
        <info-block-header label="Sizing Info"></info-block-header>
    </div>
    <div ng-if="$ctrl.clothes.length">
        <div ng-repeat="clothing in $ctrl.clothes" class="row">
            <div class="col-xs-6">
                <span class="font-bold">{{clothing.title}}:</span>
            </div>
            <div class="col-xs-6">
                <span ng-class="{'text-danger': !clothing.size}">{{clothing.size || 'N/A'}}</span>
            </div>
        </div>
    </div>
    <div ng-if="!$ctrl.clothes.length">
        <div class="row">
            <div class="col-xs-12">
                <span>The current section does not contain any info.
                    The official clothing size feature is not used for this event.</span>
            </div>
        </div>
    </div>
    <div class="row" ng-if="$ctrl.member.special_sizing_requests">
        <div class="col-xs-6">
            <span class="font-bold">Special Sizing Requests:</span>
        </div>
        <div class="col-xs-6">{{$ctrl.member.special_sizing_requests}}</div>
    </div>
</div>
