angular.module('SportWrench')

.directive('tdselectable', function(MALE_CHAR, FEMALE_CHAR) {
    var CLOSED_DIVISION_WARNING = 'You are removing your team from a division that has been closed. ' + 
                                  'You will NOT be able to re-enter this division at a later date. ' + 
                                  'Are you sure you want to change divisions or withdraw?'
    return {
        restrict: 'A',
        scope: {
            val: '=',
            onValueChanged: '&',
            item: '=',
            prop: '@',
            dropdown: '=',
            isShown: '@'
        },
        templateUrl: 'components/td/tdselectable.html',
        link: function ($scope) {
            $scope.edit             = {};
            $scope.edit.isEditing   = false;
            $scope.not_found        = false;
            $scope.divisions        = [];            
          
            $scope.$watch('dropdown', function (v) {
                if(!v) return;
                angular.copy($scope.dropdown, $scope.divisions);
                $scope.findName($scope.val);
                if($scope.not_found) {
                    $scope.divisions.push({
                        id: $scope.item.division_id,
                        name: $scope.item.division_name + ' (FULL-CLOSED)'
                    });
                }
            })

            $scope.divisions_filter = function () {
                return function (d) {
                    if (d.id < 0) return true;
                    
                    // check divisions when teams age equal to 'Adult'. Skip when division age not equal to 'Adult'.
                    if(Number($scope.item.age) === 0) {
                        return Number(d.max_age) === 0;
                    }
                    
                    // skip divisions when division age less teams age
                    if (parseInt(d.max_age, 10) < parseInt($scope.item.age,10)) return false;
                    
                    if ($scope.item.gender === d.gender) return true;

                    // Coed Divisions rules
                    if (d.gender === 'coed') {

                        // Coed Division can accept female teams with any age
                        if ($scope.item.gender === 'female') return true;

                        // Coed Division can accept male teams with any age
                        if ($scope.item.gender === 'male') return true;
                    }

                    if($scope.not_found) return true;

                    return false;
                }
            }

            $scope.findName = function (id) {
                if (!$scope.dropdown.length) return 'loading...';
                for (var i = 0, l = $scope.dropdown.length, int_id = parseInt(id, 10), dd; i < l; ++i) {
                    dd = $scope.dropdown[i];
                    if (dd.id === int_id) {
                        $scope.not_found = false;
                        return __getGenderIcon(dd.gender) + ' ' + dd.name;
                    }
                }
                $scope.not_found = true;
                return __getDivisionName();
            }

            function __getDivisionName() {
                let genderIcon = $scope.item.gender ? (__getGenderIcon($scope.item.gender) + ' ') : '';

                return genderIcon + $scope.item.division_name || 'Not found.'
            }

            $scope.order_divisions = function(division) {          
                return (division.id >= 0);
            }

            $scope.switchMode = function() {
                $scope.edit.isEditing = !$scope.edit.isEditing;
            }

            var __checkRemovalFromClosed = function (id) {
                var divisions = $scope.divisions, d;

                for(var i = 0, l = $scope.divisions.length; i < l; ++i) {
                    if(divisions[i].id === id) {
                        d = divisions[i];
                        break;
                    }
                }
   
                if(d && d.closed)
                    return confirm(CLOSED_DIVISION_WARNING);
                return true;
            }

            $scope.changeValue = function(value, oldValue) {
                console.log($scope.prop, value, oldValue);
                var oldDivisionId = parseInt(oldValue, 10);

                if($scope.not_found) {
                    angular.copy($scope.dropdown, $scope.divisions);
                }

                if(!__checkRemovalFromClosed(oldDivisionId)) {
                    this.val = oldDivisionId
                    return;
                }

                $scope.onValueChanged({
                    prop    : $scope.prop,
                    val     : value,
                    item    : $scope.item,
                    callback: (upd_team) => {
                        const failed = upd_team && upd_team.failed;

                        if (failed) {
                            this.val = oldDivisionId;
                            return;
                        }

                        var divisionId = upd_team && parseInt(upd_team.division_id, 10);
                        if(!divisionId)                                                      
                            return ($scope.val = oldDivisionId);
                        if($scope.item.deleted) {
                            $scope.item.deleted = null;
                        } else if(divisionId === -1) {
                            $scope.item.deleted = new Date();
                        }

                        $scope.item.division_id = divisionId;
                        if(upd_team.status_paid) $scope.item.status_paid = upd_team.status_paid;
                        if(upd_team.status_entry) $scope.item.status_entry = upd_team.status_entry;
                        if(upd_team.division_name) $scope.item.division_name = upd_team.division_name;
                    }
                });
            };

            var __getGenderIcon = function (g) {
                return (g)
                            ?(g === 'female')
                                ?(FEMALE_CHAR)
                                :(g === 'male')
                                    ?(MALE_CHAR)
                                    :(MALE_CHAR + FEMALE_CHAR)
                            :''
            }

            $scope._label = function (division) {
                return __getGenderIcon(division.gender) + ' ' + division.name + (division.closed?' (FULL-Closed)':'');
            }
        }
    }
});
