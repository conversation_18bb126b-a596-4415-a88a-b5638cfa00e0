<div class="row row-space">
    <div class="col-xs-12">
        <manual-add-member-list members="$ctrl.members" on-remove="$ctrl.removeMember(member)"></manual-add-member-list>
    </div>
    <div class="col-xs-12">
        <div class="row">
            <div class="col-xs-10 col-xs-offset-1">
                <button class="btn btn-default" ng-click="$ctrl.toggleAdditionMenu()">
                    <i class="{{$ctrl.getToggleButtonClass()}}"
                       aria-hidden="true"></i> Add Member
                </button>
            </div>
        </div>
        <manual-add-member-form
            ng-if="$ctrl.isAdditionMenuOpen"
            on-save="$ctrl.addMember(member)"
        ></manual-add-member-form>
    </div>
</div>
