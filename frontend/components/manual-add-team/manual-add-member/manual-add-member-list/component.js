
class Controller {

    constructor (STAFF_ROLES) {
        this.STAFF_ROLES = STAFF_ROLES;
    }

    $onChanges (changes) {
        if(changes && changes.members) {
            this.members = changes.members.currentValue;
        }
    }

    getRoleLabel (roleID) {
        let [role] = this.STAFF_ROLES.filter(r => r.id === roleID);

        return role.short_name;
    }

    remove (member) {
        this.onRemove({ member });
    }
}

Controller.$inject = ['STAFF_ROLES'];

angular.module('SportWrench').component('manualAddMemberList', {
    templateUrl: 'components/manual-add-team/manual-add-member/manual-add-member-list/template.html',
    bindings: {
        members: '<',
        onRemove: '&'
    },
    controller: Controller
});
