<table class="table table-hover table-condensed">
    <thead>
        <tr>
            <th><input type="checkbox" ng-model="vm.pick.all" ng-change="vm.pickAll()" ng-if="!vm.hidePicker && vm.teams.length"></th>
            <th>Team Info</th>
            <th>Fee</th>
            <th>Paid</th>
            <th>Discount</th>
            <th>Surcharge</th>
            <th>To Pay</th>
        </tr>
    </thead>
    <tbody>
        <tr ng-repeat="t in vm.teams | orderBy:['division_name', 'team_name']"
            ng-class="vm.rowClass(t)">
            <td><input type="checkbox" ng-model="vm.pick.items[t.roster_team_id]" ng-change="vm.toggleTeam(t)" ng-if="!vm.hidePicker"></td>
            <td>
                <span ng-bind="vm.teamLabel(t)"></span>
                <span ng-if="t.is_declined" class="text-danger">(declined)</span>
            </td>
            <td ng-bind="vm.getFee(t)"></td>
            <td ng-bind="t.paid | currency"></td>
            <td>
                <span 
                    ng-if="!vm.hasCustomDiscount" 
                    ng-bind="t.discount | currency"
                    ng-class="{ 'text-success': t.discount > 0 }"
                ></span>
                <span ng-if="vm.hasCustomDiscount">
                    <input 
                        type="number" 
                        ng-model="vm.discounts[t.roster_team_id]" 
                        ng-model-options="{ debounce: 250 }"
                        ng-disabled="!vm.pick.items[t.roster_team_id]"
                        ng-change="vm.onTeamDiscountChange(t)"
                        min="0"
                        max="{{vm.getTeamRegFee(t)}}"
                        class="form-control form-control-select-small--59">
                </span>
            </td>
            <td ng-bind="(t.div_credit_surcharge || vm.surcharge) | currency"></td>
            <td class="bg-amount text-center font-bold" ng-bind="vm.getDue(t)"></td>
        </tr>
        <tr no-data-row cs="6" text="No available Teams found" ng-if="vm.teams.length === 0"></tr>
    </tbody>
</table>
<uib-alert ng-if="vm.declinedTeams.length" type="danger text-center">
    <i class="fa fa-exclamation-triangle"></i>
    <span>This invoice has</span>
    <span>{{::vm.declinedTeams.length}}</span>
    <span>declined</span>
    <span ng-if="vm.declinedTeams.length === 1">Team</span>
    <span ng-if="vm.declinedTeams.length > 1">Teams</span>
    <span>({{::vm.getDeclinedTeamNames()}}).</span>
    <span>Declined</span>
    <span ng-if="vm.declinedTeams.length === 1">team is</span>
    <span ng-if="vm.declinedTeams.length > 1">teams are</span>
    <span>removed from invoice and amount is recalculated</span>
</uib-alert>