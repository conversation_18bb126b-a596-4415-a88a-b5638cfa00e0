<div class="row row-space">
    <div class="col-sm-12">
        <a ui-state="state" class="btn btn-primary">{{(profileExists? 'Edit' : 'Create') + ' Exhibitor / Sponsor Profile'}}</a>
    </div>
</div>
<div class="row" ng-show="profileExists === true">
    <div class="col-sm-12">
        <div class="row">
            <div class="col-sm-2"><b>Company Name:</b></div>
            <div class="col-sm-4">{{profile.company_name}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>Company Description:</b></div>
            <div class="col-sm-4">{{profile.company_description}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>Company Badge Names:</b></div>
            <div class="col-sm-4">{{profile.badge_names}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>First Name:</b></div>
            <div class="col-sm-4">{{profile.first}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>Last Name:</b></div>
            <div class="col-sm-4">{{profile.last}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>Email:</b></div>
            <div class="col-sm-4">{{profile.email}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>Mobile Phone:</b></div>
            <div class="col-sm-4">{{profile.mobile_phone}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>Office Phone:</b></div>
            <div class="col-sm-4">{{profile.office_phone}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>Street:</b></div>
            <div class="col-sm-4">{{profile.street}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>City:</b></div>
            <div class="col-sm-4">{{profile.city}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>State:</b></div>
            <div class="col-sm-4">{{profile.state}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>ZIP:</b></div>
            <div class="col-sm-4">{{profile.zip}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>Is Exhibitor:</b></div>
            <div class="col-sm-4">{{profile.is_exhibitor ? 'Yes' : 'No'}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>Is Sponsor:</b></div>
            <div class="col-sm-4">{{profile.is_sponsor ? 'Yes' : 'No'}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>Is Non-Profit:</b></div>
            <div class="col-sm-4">{{profile.is_non_profit ? 'Yes' : 'No'}}</div>
        </div>
        <div class="row">
            <div class="col-sm-2"><b>Is Other:</b></div>
            <div class="col-sm-4">{{profile.is_other ? 'Yes' : 'No'}}</div>
        </div>
    </div>
</div>
<p class="text-danger" ng-show="profileExists === false">Your Profile is not yet created. Use the button above to set up your profile.</p>
<p class="text-danger" ng-show="profileExists === undefined">Loading data...</p>
