angular.module('SportWrench').controller('InfoExhibitorController', InfoExhibitorController);
function InfoExhibitorController ($scope, $http, $stateParams, $uibModalInstance) {
    var exhibitorId = $stateParams.exhibitor;
    $scope.payments = [];
    $scope.sponsor  = {};
    $http.get('/api/sponsor/' + exhibitorId + '/info')
    .success(function (data) {
        $scope.payments = data.payments;
        $scope.sponsor = data.sponsor;
    });

    $scope.cancel = function () {
        $uibModalInstance.close();
    }
}
