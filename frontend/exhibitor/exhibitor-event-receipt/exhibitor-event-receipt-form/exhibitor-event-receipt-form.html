<div class="row">
    <div class="col-sm-12">
        <spinner active="$ctrl.loading.inProcess"></spinner>
        <uib-alert type="danger text-center" ng-if="$ctrl.loading.error">{{$ctrl.loading.error}}</uib-alert>
    </div>
    <div class="alert alert-warning" ng-if="$ctrl.isCreateMode && !$ctrl.isApplicationApproved()">
        The Application Status must be approved to generate an invoice.
    </div>
    <div ng-if="!$ctrl.loading.inProcess && !$ctrl.loading.error" class="col-sm-12">
        <form class="exhibitor-event_registration-form">
            <div class="row exhibitor-apply-form-application_row validation-required">
                <label class="col-sm-3 control-label text-right">
                    Dates:
                </label>
                <div class="col-sm-9">
                    <div class="row">
                        <div ng-repeat="(key, value) in $ctrl.exhibitorReceiptData.event_dates" class="col-sm-6">
                            <div class="exhibitor-event_registration-form_dates-item">
                                <label class="checkbox-inline">
                                    <input
                                        ng-model="$ctrl.exhibitorReceiptData.event_dates[key]"
                                        type="checkbox"
                                        ng-disabled="!$ctrl.isReceiptEditable() || !$ctrl.isApplicationApproved()">
                                    <span>{{key | UTCdate: $ctrl.EVENT_DATES_FORMAT}}</span>
                                </label>
                            </div>
                        </div>
                    </div>
                    <span class="text-danger" ng-if="$ctrl.isEventDatesEmpty() && $ctrl.isFormSubmitted">
                        Please select at least one date
                    </span>
                </div>
            </div>
            <div class="row exhibitor-apply-form-application_row validation-required">
                <label class="col-sm-3 control-label text-right">
                    Booth:
                </label>
                <div class="col-sm-9">
                    <div class="form-inline">
                        <div class="row">
                            <div class="col-sm-8">
                                <select
                                    ng-model="$ctrl.selectedBooth"
                                    class="form-control exhibitor-event_registration-form_booth-select"
                                    ng-options="booth as booth.label for booth in $ctrl.exhibitorReceiptData.event_booths"
                                    ng-disabled="!$ctrl.isReceiptEditable() || !$ctrl.isApplicationApproved()">
                                    <option value="" selected>Choose a Booth...</option>
                                </select>
                                <div class="exhibitor-event_registration-form_selected-booths" ng-if="$ctrl.selectedBooths.length">
                                    <div class="exhibitor-event_registration-form_selected-booths_item" ng-repeat="booth in $ctrl.selectedBooths track by $index">
                                        <span>{{booth.label}}</span>
                                        <span
                                            class="pointer text-danger"
                                            ng-click="$ctrl.removeBooth($index)"
                                            ng-if="$ctrl.isReceiptEditable()">
                                            <i class="fa fa-remove"></i>
                                        </span>
                                    </div>
                                </div>
                                <div class="row" ng-if="!$ctrl.selectedBooths.length && $ctrl.isFormSubmitted">
                                    <div class="col-sm-12">
                                        <span class="text-danger">Please add at least one booth</span>
                                    </div>
                                </div>
                            </div>
                            <div ng-if="$ctrl.isReceiptEditable() && $ctrl.exhibitorReceiptData.event_booths.length" class="col-sm-4">
                                <button ng-click="$ctrl.addBooth()" ng-disabled="!$ctrl.isApplicationApproved()" class="btn btn-primary to-right">Add Booth</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row exhibitor-apply-form-application_row">
                <div class="col-sm-3 text-right"><span class="text-bold">Comment:</span></div>
                <div class="col-sm-6">
                    <textarea
                        ng-model="$ctrl.exhibitorReceiptData.comment"
                        class="full-width form-control"
                        rows="1"
                        ng-disabled="!$ctrl.isReceiptEditable() || !$ctrl.isApplicationApproved()">
                    </textarea>
                </div>
            </div>
            <div ng-if="$ctrl.getBoothsTotal()" class="row exhibitor-apply-form-application_row">
                <div class="col-sm-3 text-right">
                    <span class="text-bold">Total:</span>
                </div>
                <div class="col-sm-9">
                    <span class="text-bold">{{$ctrl.getBoothsTotal() | currency}}</span>
                </div>
            </div>
        </form>
    </div>
</div>
