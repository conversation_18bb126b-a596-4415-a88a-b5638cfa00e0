angular.module('SportWrench').controller('Club.Event.MembersList', MembersList);

MembersList.$inject = [
    '$scope', 'athletesService', 'teamsList', '$stateParams', 'ClubTeamsService', '$uibModal', '$filter', 'loadClub', 'masterClubService',
    'GENDER_VALUES', 'SANCTIONING_BODY'
];

function MembersList (
    $scope, athletesService, teamsList, $stateParams, ClubTeamsService, $uibModal, $filter, loadClub, masterClubService,
    GENDER_VALUES, SANCTIONING_BODY
) {
    $scope.GENDER_VALUES = GENDER_VALUES;

    $scope.members = [];
    $scope.teams = teamsList.list;
    $scope.team = {};
    $scope.order = {
        reverse: false
    };
    $scope.filters = {
        team: (teamsList.choosen || $scope.teams && $scope.teams[0] || {})
    };
    $scope.loading = { members_loaded: false, teams_loaded: false };
    $scope.jerseyDups = {};
    $scope.aauJerseyDups = {};
    $scope.eventId = $stateParams.event;
    $scope.utils = {
        accordOpen          : false,
        checkinValidation   : {}
    }
    $scope.clubHas9ManSanctioning = masterClubService.clubHas9ManSanctioning(loadClub);
    $scope.eventHasAAUSanctioning = $scope.$parent.event.sport_sanctioning_id === SANCTIONING_BODY.AAU;

    function _loadMembers (team_id) {
         $scope.loading.members_loaded = false;
         ClubTeamsService.roster.membersList($stateParams.event, team_id)
         .success(function (data) {
             $scope.members = data.members;
             $scope.team = data.team;
             $scope.utils.checkinValidation = data.checkinValidation;
             $scope.loading.members_loaded = true;
             _findJerseyDuplicates();
             _findAAUJerseyDuplicates();
         })
    }

    function _getJersey (m) {
        return m.jersey || m.default_jersey
    }

    function _getAAUJersey (m) {
        return m.aau_jersey || m.default_aau_jersey
    }

    function _findJerseyDuplicates () {
        var membersIds = Object.keys($scope.jerseyDups);

        for(var i = 0, l = membersIds.length; i < l; ++i)
            $scope.jerseyDups[membersIds[i]] = false;

        for(var i = 0, l = $scope.members.length, currentMember, currentJersey, dupMember, dupJersey; i < l; ++i) {
            currentMember = $scope.members[i];
            currentJersey = _getJersey(currentMember);

            if(currentMember.role !== 'athlete' || !currentJersey|| currentMember.deleted_by_user)
                continue;

            for(var j = (i + 1); j < l; ++j) {
                dupMember = $scope.members[j];
                dupJersey = _getJersey(dupMember)
                if(!dupMember.deleted_by_user && (dupJersey === currentJersey)) {
                    $scope.jerseyDups[dupMember.id] = true;
                    $scope.jerseyDups[currentMember.id] = true;
                }
            }
        }
    }

    function _findAAUJerseyDuplicates () {
        const membersIds = Object.keys($scope.aauJerseyDups);

        for(let i = 0, l = membersIds.length; i < l; ++i) {
            $scope.aauJerseyDups[membersIds[i]] = false;
        }

        for(let i = 0, l = $scope.members.length, currentMember, currentAAUJersey,
                dupMember, dupAAUJersey; i < l; ++i) {
            currentMember = $scope.members[i];
            currentAAUJersey = _getAAUJersey(currentMember);

            if(currentMember.role !== 'athlete' || !currentAAUJersey || currentMember.deleted_by_user) {
                continue;
            }

            for(let j = (i + 1); j < l; ++j) {
                dupMember = $scope.members[j];
                dupAAUJersey = _getAAUJersey(dupMember)
                if(!dupMember.deleted_by_user && (dupAAUJersey === currentAAUJersey)) {
                    $scope.aauJerseyDups[dupMember.id] = true;
                    $scope.aauJerseyDups[currentMember.id] = true;
                }
            }
        }
    }

    $scope.empty = function (obj) {
        return _.isEmpty(obj)
    }

    $scope.findJerseyDuplicates = function () {
        ClubTeamsService.roster.validateJerseys($stateParams.event, $scope.filters.team.roster_team_id)
        .then(function (resp) {
            var jerseyValidation = resp && resp.data.jersey;
            if(!jerseyValidation) {
                delete $scope.utils.checkinValidation.jersey;
            } else {
                $scope.utils.checkinValidation.jersey = jerseyValidation
            }
            $scope.utils.checkinValidation = _.pick($scope.utils.checkinValidation, _.identity)
        })
        _findJerseyDuplicates();
    }

    $scope.findAAUJerseyDuplicates = function () {
        ClubTeamsService.roster.validateAAUJerseys($stateParams.event, $scope.filters.team.roster_team_id)
            .then(function (resp) {
                const aauJerseyValidation = resp && resp.data.aauJersey;
                if(!aauJerseyValidation) {
                    delete $scope.utils.checkinValidation.aau_jersey;
                } else {
                    $scope.utils.checkinValidation.aau_jersey = aauJerseyValidation
                }
                $scope.utils.checkinValidation = _.pick($scope.utils.checkinValidation, _.identity)
            })
        _findAAUJerseyDuplicates();
    }

    $scope.loadMembers = function () {
        if(_.isEmpty(this.filters.team)) return;
        _loadMembers(this.filters.team.roster_team_id);
    }

    $scope.order = function (col) {
        $scope.order.column = col;
        $scope.order.reverse = !$scope.order.reverse;
    }

    $scope.order_value = function () {
        return $scope.order.reverse?$scope.order.column:('-' + $scope.order.column)
    }

    $scope.role_order_value = function () {
        if($scope.order.column === 'role' && !$scope.order.reverse) return '-role';
        return 'role';
    }

    $scope.openMemberModal = function (member) {
        $scope.memberType = ((member.role === 'athlete')?'j':'s');
        $scope.memberId = member.id;

        $uibModal.open({
            scope: $scope,
            template: 
                `<event-member-info
                    event-name="$parent.event.name"
                    team-name="filters.team.name"
                    deadline-passed="team.deadline_passed"
                    roster-locked="team.locked"
                    deadline="team.deadline"
                    online-checkin-date="team.online_checkin_date"
                    member-id="memberId"
                    member-type="memberType"
                    club-has-nine-man-sanctioning="clubHas9ManSanctioning"
                    event-has-aau-sanctioning="eventHasAAUSanctioning"
                </event-member-info>`
        })
    }

    $scope.printRoster = function () {
        if($scope.empty($scope.utils.checkinValidation)) {
            ClubTeamsService.roster.showCheckInList($stateParams.event, $scope.filters.team.roster_team_id)
        } else {
            var t = angular.copy($scope.filters.team);
            t.team_name = t.name;
            ClubTeamsService.roster.validationModal(
                $scope.utils.checkinValidation, {
                    team        : t,
                    event_id    : $stateParams.event,
                    showLink    : false
                }
            )
        }
    }
    
    $scope.loadMembers();

    $scope.memberRemovedText = function (m) {
        return (m.deleted_by_user)
            ?(m.first + ' ' + m.last + ' was removed from this Event at ' + m.deleted_by_user)
            :''
    }

    $scope.jerseyDupWarn = function (id) {
        return (
            $scope.jerseyDups[id]?'Duplicate Uniform':''
        )
    }

    $scope.aauJerseyDupWarn = function (id) {
        return (
            $scope.aauJerseyDups[id]?'Duplicate AAU Uniform':''
        )
    }

    $scope.positionCellClick = function (m) {
        if(!m.is_athlete || $scope.team.deadline_passed) {
            $scope.openMemberModal(m)
        }
    }

    $scope.roleCellClick = function (m) {
        if($scope.team.deadline_passed) {
            $scope.openMemberModal(m)
        }
    }

    // NOTE: ng-class didn't work for "uib-accordion-group" directive
    $scope.valPanelClass = function () {
        return (
            'payments-list--sm panel-danger' + 
            (!$scope.utils.accordOpen?' center-form-elem-v':'')
        );
    }

    $scope.$on('club.event-roster.members.reload', function () {
        $scope.loadMembers();
    })

    $scope.showTravelCoordinator = function () {
        return $scope.$parent.event.housing_company_id === 1 && !isTravelCoordInfoEmpty();
    }

     function isTravelCoordInfoEmpty() {
        let email = $scope.filters.team.ths_trav_coord_email;
        let phone = $scope.filters.team.ths_trav_coord_phone;
        let name  = $scope.filters.team.ths_trav_coord_name;

        return !email && !phone && !name;
    }

    $scope.travelCoordinatorContacts = function () {
        let email = $scope.filters.team.ths_trav_coord_email;
        let phone = $scope.filters.team.ths_trav_coord_phone;
        let name  = $scope.filters.team.ths_trav_coord_name;

        let phoneFilter = $filter('tel');

        return `
            <p>${name}</p>
            <p><a href="mailto:'${email}'">${email}</a></p>
            <p><b>Cell:</b> <a href="tel:'${phone}'">${phoneFilter(phone)}</a></p>
        `;
    }
}
