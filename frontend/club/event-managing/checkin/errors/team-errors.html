<div ng-if="errors.jersey">
	<b>Jersey Duplicates:</b>
	<ul class="list-group">
	  <li class="list-group-item" ng-repeat="j in errors.jersey">
	  	<b>Jersey: {{j.jersey}}</b>, Athletes:
	  	<span ng-repeat="a in j.athletes">{{($index > 0)?', ':''}}{{a.name}}</span>
	  </li>
	</ul>
</div>
<div ng-if="errors.aau_jersey">
	<b>AAU Jersey Duplicates:</b>
	<ul class="list-group">
	  <li class="list-group-item" ng-repeat="j in errors.aau_jersey">
	  	<b>AAU Jersey: {{j.aau_jersey}}</b>, Athletes:
	  	<span ng-repeat="a in j.athletes">{{($index > 0)?', ':''}}{{a.name}}</span>
	  </li>
	</ul>
</div>
<div ng-if="errors.empty_uniform">
	<b>Empty Uniform Number:</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="m in errors.empty_uniform" ng-bind="m.name"></li>
	</ul>
</div>
<div ng-if="errors.empty_pos">
	<b>Empty Position:</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="m in errors.empty_pos" ng-bind="m.name"></li>
	</ul>
</div>
<div ng-if="errors.hc_not_one">
    <b>Team must have one Head Coach</b>
    <ul class="list-group">
        <li class="list-group-item">Head Coach Count allowed is 1, Team has {{errors.hc_not_one}}</li>
    </ul>
</div>
<div ng-if="errors.hc_unique">
	<b>Head Coach must be unique to one team entered in event</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="m in errors.hc_unique">
			<b>{{m.name}}: </b>
			<span ng-repeat="t in m.dups">{{($index > 0)?', ':''}}{{t.name}}</span>
		</li>
	</ul>
</div>
<div ng-if="errors.impact">
	<b>Head/Assistant Coaches must have IMPACT Certification:</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="m in errors.impact">
			<span ng-bind="m.name" class="font-bold"></span>
			<span ng-bind="m.role_name"></span>
			<span ng-if="m.cert"></span>
		</li>
	</ul>
</div>
<div ng-if="errors.hc_cell_phone">
	<b>Head Coach must have Cell Phone:</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="m in errors.hc_cell_phone" ng-bind="m.name"></li>
	</ul>
</div>
<div ng-if="errors.roster">
	<b>Members Quantity Violation:</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="msg in errors.roster" ng-bind="msg"></li>
	</ul>
</div>
<div ng-if="errors.birthdate">
	<b>Following Members has no Birthdate:</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="m in errors.birthdate" ng-bind="m.name"></li>
	</ul>
</div>
<div ng-if="errors.assignedRole">
	<b>The following staff members must have role:</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="m in errors.assignedRole" ng-bind="m.name"></li>
	</ul>
</div>
<div ng-if="errors.bg">
	<b>Following Staff members have no Background Screening</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="m in errors.bg" ng-bind="m.name"></li>
	</ul>
</div>
<div ng-if="errors.safesport">
	<b>Following Staff members have no SafeSport certification:</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="m in errors.safesport" ng-bind="m.name"></li>
	</ul>
</div>
<div ng-if="errors.adult_athletes_ss">
    <b>Athletes who do not have SafeSport certificate and will be 18 years old by end of event:</b>
    <ul class="list-group">
        <li class="list-group-item" ng-repeat="a in errors.adult_athletes_ss" ng-bind="a.name"></li>
    </ul>
</div>
<div ng-if="errors.not_valid_primary_staffers">
    <b>Following Primary Staff members have no Email or Phone Number:</b>
    <ul class="list-group">
        <li class="list-group-item" ng-repeat="m in errors.not_valid_primary_staffers">
            {{m.name}}:
            <b>{{m.has_no_phone ? ' No phone' : ''}}</b>
            <b>{{m.has_no_phone && m.has_no_email ? 'and' : ''}}</b>
            <b>{{m.has_no_email ? ' No email' : ''}}</b>
        </li>
    </ul>
</div>
<div ng-if="errors.staffer_phone">
	<b>Staffer must have Cell Phone Number:</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="m in errors.staffer_phone">
			{{m.name}} - {{m.same?'Cell Phone Number equals to the Head Coach\'es Number':'Empty Phone Number'}}
		</li>
	</ul>
</div>
<div ng-if="errors.invalid_primary">
	<b>Primary Team Constraint:</b>
	<ul class="list-group">
		<li class="list-group-item" ng-repeat="m in errors.invalid_primary">
			<b>{{m.name}}</b>
			<span ng-if="!m.primaries.length">Has no Primary Team</span>
			<span ng-if="m.primaries.length">
				Has multiple Primary Teams:
				<span ng-repeat="p in m.primaries">{{($index > 0)?', ':''}}{{p.name}}</span>
			</span>
		</li>
	</ul>
</div>
<div ng-if="errors.unic_name">
    <b>Team Name Duplicates:</b>
    <ul class="list-group">
        <li class="list-group-item" ng-repeat="un in errors.unic_name" ng-bind="un"></li>
    </ul>
</div>
<div ng-if="errors.staff_ref_certs_count">
	<b>Staff Referee Certification:</b>
	<ul class="list-group">
		<li class="list-group-item">At least <b>{{errors.staff_ref_certs_count.shouldBe}}</b> staff must have Referee certification.<br>
										Staff with Referee Certification: <b>{{errors.staff_ref_certs_count.actual}}</b></li>
	</ul>
	</div>
<div ng-if="errors.staff_score_keep_certs_count">
	<b>Staff Score Keeping Certification:</b>
	<ul class="list-group">
		<li class="list-group-item">At least <b>{{errors.staff_score_keep_certs_count.shouldBe}}</b> staff must have Score Keeping certification.<br>
											Staff with Score Keeping Certification: <b>{{errors.staff_score_keep_certs_count.actual}}</b></li>
	</ul>
</div>
<div ng-if="errors.players_ref_certs_count">
    <b>Players' Referee Certification:</b>
    <ul class="list-group">
        <li class="list-group-item">At least <b>{{errors.players_ref_certs_count.shouldBe}}</b> player(s) must have Referee certification.<br>
									Players with Referee Certification: <b>{{errors.players_ref_certs_count.actual}}</b></li>
    </ul>
</div>
<div ng-if="errors.players_score_keep_certs_count">
	<b>Players' Score Keeping Certification:</b>
	<ul class="list-group">
		<li class="list-group-item">At least <b>{{errors.players_score_keep_certs_count.shouldBe}}</b> player(s) must have Score Keeping certification.<br>
										Players with Score Keeping Certification: <b>{{errors.players_score_keep_certs_count.actual}}</b></li>
	</ul>
</div>
<div ng-if="errors.usav_seasonality">
    <b>Following Athletes have invalid USAV Seasonality (team's - {{errors.usav_seasonality[0].team_seasonality}}):</b>
    <ul class="list-group">
        <li class="list-group-item" ng-repeat="a in errors.usav_seasonality"><b>{{a.athlete.name}}</b> - {{a.athlete.seasonality}}</li>
    </ul>
</div>
<div ng-if="errors.player_usav_membership_status">
    <b>Following Athletes have invalid USAV eligibility status:</b>
    <ul class="list-group">
        <li class="list-group-item" ng-repeat="a in errors.player_usav_membership_status"><b>{{a.name}}</b> - {{a.membership_status}}</li>
    </ul>
</div>
<div ng-if="errors.staff_usav_membership_status">
    <b>Following Staffers have invalid USAV eligibility status:</b>
    <ul class="list-group">
        <li class="list-group-item" ng-repeat="s in errors.staff_usav_membership_status"><b>{{s.name}}</b> - {{s.membership_status}}</li>
    </ul>
</div>
<div ng-if="errors.athlete_sanctioning">
    <b>Following Athletes have invalid sanctioning:</b>
    <ul class="list-group">
        <li class="list-group-item" ng-repeat="athlete in errors.athlete_sanctioning.athletes">
            <span ng-switch on="errors.athlete_sanctioning.sportSanctioning">
                <span ng-switch-when="JVA">
                    Athlete {{athlete.name}} should have AAU sanctioning for JVA event
                </span>
                <span ng-switch-when="AAU">
                    Athlete {{athlete.name}} should have AAU sanctioning for AAU event
                </span>
                <span ng-switch-when="USAV">
                    Athlete {{athlete.name}} should have USAV sanctioning for USAV event
                </span>
                <span ng-switch-when="NINE_MAN">
                    Athlete {{athlete.name}} should not have USAV and AAU sanctioning for 9 Man event
                </span>
                <span ng-switch-default>
                    Athlete {{athlete.name}} should not have USAV and AAU sanctioning for event
                </span>
            </span>
        </li>
    </ul>
</div>
<div ng-if="errors.staff_sanctioning">
    <b>Following Staffers have invalid sanctioning:</b>
    <ul class="list-group">
        <li class="list-group-item" ng-repeat="staffer in errors.staff_sanctioning.staffers">
            <span ng-switch on="errors.staff_sanctioning.sportSanctioning">
                <span ng-switch-when="JVA">
                    Staffer {{staffer.name}} should have AAU sanctioning for JVA event
                </span>
                <span ng-switch-when="AAU">
                    Staffer {{staffer.name}} should have AAU sanctioning for AAU event
                </span>
                <span ng-switch-when="USAV">
                    Staffer {{staffer.name}} should have USAV sanctioning for USAV event
                </span>
                <span ng-switch-when="NINE_MAN">
                    Staffer {{staffer.name}} should not have USAV and AAU sanctioning for 9 Man event
                </span>
                <span ng-switch-default>
                    Staffer {{staffer.name}} should not have USAV and AAU sanctioning for event
                </span>
            </span>
        </li>
    </ul>
</div>
<div ng-if="errors.staff_roles_allowed">
    <b>Staff Roles Violation:</b>
    <ul class="list-group">
        <li class="list-group-item" ng-repeat="msg in errors.staff_roles_allowed" ng-bind="msg"></li>
    </ul>
</div>
