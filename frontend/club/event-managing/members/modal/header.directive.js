angular.module('SportWrench').directive('eventMemberHeader', function () {
    return {
        restrict: 'E',
        scope: {
            first           : '=',
            last            : '=',
            eventName       : '@',
            teamName        : '@'
        },
        replace: true,
        templateUrl: 'club/event-managing/members/modal/header.html',
        link: function (scope) {
            scope.getName = function () {
                if(scope.first && scope.last)
                    return scope.first + ' ' + scope.last
                return '';
            }
            scope.getSubtitle = function () {
                return scope.eventName + ' (' + scope.teamName + ')'
            }
        }
    }
})
