<uib-tabset active="tabs.active">
    <uib-tab index="0" heading="New payment" select="loadTeams()">
        <teams-charge ng-if="tabs.active == 0 && !useTeamPaymentForm(eventInfo)"
                      teams="teamsList"
                      event="eventInfo"
                      master-club="masterClub"
                      create="pay(payment)"
        ></teams-charge>

        <team-payment-form ng-if="tabs.active == 0 && useTeamPaymentForm(eventInfo)"
                           teams="teamsList"
                           event="eventInfo"
                           pay-check="pay(payment)"
        ></team-payment-form>
    </uib-tab>
    <uib-tab index="1" heading="Payment history" select="loadHistory()">

        <club-purchases list="history.payments"
                        ng-if="!history.loading && history.payments.length"
                        change-method="openChangeMethodMenu(id)"
                        cancel-payment="cancelPayment(id)"
                        allow-change="history.allowTypeChange"
        ></club-purchases>

        <uib-alert type="warning text-center row-space" ng-if="!(history.loading || history.payments.length)">
            No Purchases
        </uib-alert>

        <spinner active="history.loading"></spinner>
    </uib-tab>
    <uib-tab index="2" ng-if="typeChange.payment.id">
        <uib-tab-heading>
            Payment Method Change <i class="fa fa-times fa-lg text-danger pointer" ng-click="closeTypeChange()"></i>
        </uib-tab-heading>
        <uib-alert type="info text-center spacer-sm-t">
            <i class="fa fa-info-circle"></i>
            <span>You are about to change the payment method for charge <b>#{{typeChange.payment.id}}</b> </span><br/>
            <span ng-if="typeChange.payment.created">created at <b>{{typeChange.payment.created}}</b></span>
        </uib-alert>
        <teams-charge ng-if="tabs.active == 2 && !useTeamPaymentForm(typeChange.eventInfo)"
                      teams="typeChange.teamsList"
                      event="typeChange.eventInfo"
                      master-club="masterClub"
                      create="changeType(payment)"
                      disable-checkboxes="true"
        ></teams-charge>

        <team-payment-form ng-if="tabs.active == 2 && useTeamPaymentForm(typeChange.eventInfo)"
                           teams="typeChange.teamsList"
                           event="typeChange.eventInfo"
                           type-change-payment="typeChange.payment"
        ></team-payment-form>
    </uib-tab>
</uib-tabset>

<script type="text/ng-template" id="group-tabs.html">
    <div>
        <div class="row row-space">
            <div class="col-xs-12">
                <div class="btn-group btn-group-sm pull-right" ng-transclude></div>
            </div>
        </div>
        <div class="tab-content">
          <div class="tab-pane" ng-repeat="tab in tabset.tabs" 
                ng-class="{active: tabset.active === tab.index}"
                uib-tab-content-transclude="tab">
          </div>
        </div>
    </div>
</script>

<script type="text/ng-template" id="group-tab.html">
    <a href
       ng-click="select($event)"
       ng-class="[{active: active, disabled: disabled}, classes]"
       class="btn btn-primary"
       uib-tab-heading-transclude>{{heading}}</a>
</script>
