<modal-wrapper>
    <spinner active="$ctrl.invoiceLoading"></spinner>
    <overlay-with-spinner is-shown="$ctrl.paymentIsInProgress"></overlay-with-spinner>
    <div ng-show="!$ctrl.invoiceLoading">
        <div class="invoice-data-list col-xs-offset-3">
            <div class="row">
                <div class="col-xs-4">
                    <strong>Event Name:</strong>
                </div>
                <div class="col-xs-5">{{$ctrl.invoiceData.eventName}}</div>
            </div>
            <div class="row">
                <div class="col-xs-4">
                    <strong>Description:</strong>
                </div>
                <div class="col-xs-5">{{$ctrl.invoiceData.description}}</div>
            </div>
            <div class="row">
                <div class="col-xs-4">
                    <strong>Subtotal:</strong>
                </div>
                <div class="col-xs-5">${{$ctrl.payment.subtotal}}</div>
            </div>
            <div class="row">
                <div class="col-xs-4">
                    <span>Merchant Fee:</span>
                </div>
                <div class="col-xs-5">${{$ctrl.payment.merchantFee[$ctrl.paymentType]}}</div>
            </div>
            <div class="row">
                <div class="col-xs-4">
                    <strong>Total:</strong>
                </div>
                <div class="col-xs-5">${{$ctrl.payment.total[$ctrl.paymentType]}}</div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-offset-1 col-xs-10">
                <div class="row row-space" ng-if="$ctrl.tooSmallAmount()">
                    <uib-alert type="danger">
                        <div class="row">
                            <div class="col-sm-12 text-center">
                                Sorry, selected payment method can be used for payments with min {{$ctrl.minAmountForSelectedPaymentMethod | currency}} amount!
                            </div>
                        </div>
                    </uib-alert>
                </div>
                <div class="row stripe-card-form" ng-if="!$ctrl.hidePaymentForm">
                    <div class="col-xs-12">
                        <form id="payment-form">
                            <stripe-card-list
                                disabled="$ctrl.paymentIsInProgress"
                                cards="$ctrl.savedCards"
                                selected-card="$ctrl.selectedCard"
                                on-card-select="$ctrl.onCardSelect(card)"
                                on-card-reset="$ctrl.onCardReset()"
                                ng-if="$ctrl.invoiceData.allowCardPayments && $ctrl.savedCards.length > 0"
                            >
                            </stripe-card-list>
                            <div id="payment-element" ng-hide="$ctrl.isCardSelected()">
                                <!-- Elements will create form elements here -->
                            </div>
                            <button class="btn" ng-style="$ctrl.isCardSelected() && {'margin-top': 0}" type="submit" ng-click="$ctrl.submit()"
                                    ng-disabled="!$ctrl.submitAllowed()">
                                Pay {{$ctrl.payment.total[$ctrl.paymentType] | currency}}
                                <spinner is-inline="true" pos="left" size="1" no-wrapper="true" active="$ctrl.paymentIsInProgress"></spinner>
                            </button>
                            <div id="error-message">
                                <!-- Display error message to your customers here -->
                            </div>
                        </form>
                    </div>
                </div>
                <div class="row" ng-if="$ctrl.hidePaymentForm">
                    <uib-alert type="danger">
                        <div class="row">
                            <div class="col-sm-12 text-center">Sorry, Online Payments currently unavailable!</div>
                        </div>
                    </uib-alert>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-7 col-xs-offset-5">
                <button class="btn btn-default pull-right"
                    ng-disabled="$ctrl.paymentIsInProgress"
                    ng-click="$ctrl.close()"
                >Close</button>
            </div>
        </div>
    </div>
</modal-wrapper>
