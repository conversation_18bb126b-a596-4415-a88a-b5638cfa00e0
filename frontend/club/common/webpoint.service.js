angular.module('SportWrench').service('ClubWebpointService', ['$http', ClubWebpointService]);

function ClubWebpointService ($http) {
    this._$http     = $http;
    this._baseUrl   = '/api/club/webpoint/';
}

ClubWebpointService.prototype.syncMember = function (memberID, memberType) {
    let url = this._baseUrl + memberType + '/' + memberID + '/sync';

    return this._$http.get(url).then(resp => resp.data);
}


ClubWebpointService.prototype.syncAthlete = function (athleteID) {
    return this.syncMember(athleteID, 'athlete');
}

ClubWebpointService.prototype.syncStaffer = function (stafferID) {
    return this.syncMember(stafferID, 'staff');
}


