angular.module('SportWrench').controller('Club.Teams.ArchiveController', ArchiveController);

ArchiveController.$inject = ['archive', 'ClubTeamsService', 'INTERNAL_ERROR_MSG', 'APP_ROUTES', 'loadClub', 'masterClubService'];

// controllerAs: archive
function ArchiveController (archive, ClubTeamsService, INTERNAL_ERROR_MSG, APP_ROUTES, loadClub, masterClubService) {
    this.teams = archive.teams;
    this.season = archive.sw_season;
    this.utils = {
        all_selected: false,
        selection: {}
    };
    this.filters = {
        archived: true
    };
    this.states = {
        teams: APP_ROUTES.CD.TEAMS
    }

    this.updateList = function () {
        var self = this;
        this.utils.error = null;
        ClubTeamsService.teamsList(this.filters)
        .success(function (data) {
            self.teams = data.teams;
        }).error(function (data) {
            self.utils.error = data.validation || INTERNAL_ERROR_MSG
        });
    };

    this.clubHasUsavSanctioning = () => masterClubService.clubHasUsavSanctioning(loadClub);

    this.teamCheckChanged = function () {
        for (var i = 0, l = this.teams.length, counter = 0; i < l; ++i) {
            if(this.utils.selection[this.teams[i].id]) ++counter;
        }
        if(counter > 0) {
            this.utils.has_selections = true;
        } else {
            this.utils.has_selections = false;
        }
        if(counter !== this.teams.length)
            this.utils.all_selected = false
        else
            this.utils.all_selected = true;
    }

    this.allSelectedChanged = function () {
        if(!this.utils.all_selected) {
            this.utils.selection = {}
            this.utils.has_selections = false;
        } else {
            for (var i = 0, l = this.teams.length, q = 0; i < l; ++i) {
                if(!this.teams[i].in_current_season) {
                    this.utils.selection[this.teams[i].id] = true;
                    ++q
                }
            }
            this.utils.has_selections = (q > 0);
        }
    }

    this.copyToCurrentSeason = function () {
        var teamsToMove = [], self = this;
        for(var i = 0, keys = Object.keys(this.utils.selection), l = keys.length; i < l; ++i) {
            if(this.utils.selection[keys[i]]) teamsToMove.push(parseInt(keys[i], 10));
        }
        if(!teamsToMove.length) return;
        ClubTeamsService.copyToCurrentSeason(teamsToMove).success(function (data) {
            for(let currentTeam of self.teams) {
                for(let newTeam of data.teams) {
                    let uniquenessRules = [
                        Number(currentTeam.age) === Number(newTeam.age),
                        currentTeam.gender === newTeam.gender,
                        currentTeam.rank === newTeam.rank,
                        currentTeam.name === newTeam.name
                    ];

                    if(_.every(uniquenessRules, r => !!r)) {
                        currentTeam.in_current_season = true;
                        break;
                    }
                }
            }
            self.utils.selection = {};
        }).error(function (data) {
            self.utils.error = data.validation || INTERNAL_ERROR_MSG
        })
    }
}
