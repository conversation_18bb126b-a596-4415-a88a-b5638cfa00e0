<div class="modal-header">
    <h2 class="text-info">Import from SportEngine</h2>
</div>
<div class="modal-body">
    <div class="row row-space">
        <div class="col-xs-12">
            <club-se-import-info club-info="$ctrl.club"></club-se-import-info>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <form class="form-horizontal row-space" autocomplete="off">
                <div class="form-group">
                    <div class="well col-sm-offset-1 col-sm-10">
                        <p>Choose the most suitable import option:</p>
                        <label class="col-sm-offset-1 radio">
                            <input type="radio" name="import_option" ng-model="$ctrl.importData.option" value="insert"> Import New Athlete/Staff Only:
                            <p class="help-block">I want to update the list of currently available athletes and staff by importing members from SportEngine that are not currently in SportWrench.  New athletes/staff will be added to existing event rosters. Current athlete/staff in SportWrench will not be updated.  </p>
                        </label>
                        <label class="col-sm-offset-1 radio">
                            <input type="radio" name="import_option" ng-model="$ctrl.importData.option" value="default"> Import New Athlete/Staff and Update Current Athlete/Staff:
                            <p class="help-block">I want to update the list of currently available athletes and staff by importing members from SportEngine that are not currently in SportWrench.  I also want to update the current athlete/staff in SportWrench with values from SportEngine (i.e, update uniform number and/or team).  New athletes/staff will be added to existing event rosters and updated athlete/staff values will be added to existing rosters.</p>
                            <p class="help-block">THIS WILL REPLACE INFORMATION IN SPORTWRENCH WITH SPORTENGINE DATA IF IT EXISTS</p>
                        </label>
                    </div>
                </div>
                <div class="col-sm-10 col-md-offset-1">
                    <h3 ng-if="$ctrl.importCreated()" class="text-danger">
                        Import is still in process.
                        <span ng-if="$ctrl.queue.requested"> Requested at {{$ctrl.queue.requested}}. Please wait</span>
                    </h3>
                </div>
            </form>
        </div>
    </div>

    <blocked-events-list events="$ctrl.blockedEventsList"></blocked-events-list>
    <uib-alert type="danger" ng-if="$ctrl.utils.error" ng-bind="$ctrl.utils.error"></uib-alert>
</div>
<div class="modal-footer">
    <button type="button"
            class="btn btn-primary"
            ng-disabled="$ctrl.importCreated()"
            ng-click="$ctrl.submit()"
    >Start Import</button>

    <button type="button" class="btn btn-default" ng-click="$ctrl.close()">Close</button>
</div>
