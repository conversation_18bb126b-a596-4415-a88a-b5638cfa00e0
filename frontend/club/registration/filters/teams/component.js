
class Controller {
    constructor (masterTeamService) {
        this.masterTeamService = masterTeamService;
    }

    $onInit () {
        this.filterValues = {
            ageFilters: this.__getTeamAges(),
            rankFilters: this.__getTeamRanks(),
            teamAgeComparisonFilter: [
                { id: null, name: 'All' },
                { id: 'equal', name: '= Division Age'}
            ]
        }
    }

    onFilterChange () {
        this.onUpdate({ filters: this.filters });
    }

    __getTeamAges () {
        let ages = [
            { id: null, name: 'All' },
        ];

        for (let i = 8; i <= 18; ++i) {
            ages.push({
                id: i,
                name: i
            });
        }

        return ages;
    }

    __getTeamRanks () {
        const rankValues = this.masterTeamService.getRanks();

        let ranks = rankValues.map((item) => ({ id: item, name: item }));

        ranks.unshift({ id: null, name: 'All' });

        return ranks;

    }
}

Controller.$inject = ['masterTeamService'];

angular.module('SportWrench').component('clubBulkRegistrationTeamsFilter', {
    templateUrl: 'club/registration/filters/teams/template.html',
    bindings: {
        filters: '<',
        onUpdate: '&'
    },
    controller: Controller
});
