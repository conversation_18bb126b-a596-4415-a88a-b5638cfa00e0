angular.module('SportWrench').service('TeamsRegistrationService', ['$http', Service]);

function Service ($http) {
    this.$http = $http;
}

Service.prototype.getRegistrationList = function (filters) {
    return this.$http.get(`/api/v2/club/bulk-registration/list`, { params: filters,  paramSerializer: '$httpParamSerializerJQLike' })
        .then(response => response.data && response.data.list);
}

Service.prototype.processRegistration = function (selection) {
    return this.$http.post(`/api/v2/club/bulk-registration`, { selection })
        .then(response => response.data && response.data.list);
}
