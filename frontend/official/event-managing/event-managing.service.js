angular.module('SportWrench').service('HOEventManagementService', ['$http', '$httpParamSerializerJQLike', 'officialService', HOEventManagementService]);

function HOEventManagementService ($http, $httpParamSerializerJQLike, officialService) {
	this._$http 		= $http;
	this._baseUrl 		= '/api/official/event/';
	this._paramsSrlzr 	= $httpParamSerializerJQLike;
	this._baseStaffUrl  = 'api/staff/event';
    this._officialService = officialService;
}

HOEventManagementService.prototype.getEventOfficials = function (eventID) {
	return this._$http.get(this._baseUrl + eventID + '/officials');
};

HOEventManagementService.prototype.excelExport = function (eventID, officials) {
	window.location = this._baseUrl + eventID + '/officials/export?' + this._paramsSrlzr({ officials: officials });
};

HOEventManagementService.prototype.getOfficial = function (eventID, officialID) {
	return this._$http.get(this._baseUrl + eventID + '/official/' + officialID);
};

HOEventManagementService.prototype.updateOfficial = function (eventID, officialID, data) {
	return this._$http.put(this._baseUrl + eventID + '/official/' + officialID + '/update', data);
};

HOEventManagementService.prototype.updOfficials = function (eventID, officialsList, data) {
	return this._$http.put(this._baseUrl + eventID + '/officials/update', {
		officials 	: officialsList, 
		data 		: data
	});
};

HOEventManagementService.prototype.getStaffInfo = function(eventID, staffID) {
    return this._$http.get(`${this._baseStaffUrl}/${eventID}/staff/${staffID}`)
        .then(response => response && response.data);
};

HOEventManagementService.prototype.updateStaffInfo = function(eventID, staffID, data) {
    return this._$http.put(`${this._baseStaffUrl}/${eventID}/staff/${staffID}/update`, data);
};

HOEventManagementService.prototype.makeHead = function(eventID, officialID) {
    return this._officialService.makeHead(eventID, officialID);
};
