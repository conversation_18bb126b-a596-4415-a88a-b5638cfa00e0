angular.module('SportWrench').controller('OfficialEventPayoutsController', OfficialEventPayouts);

OfficialEventPayouts.$inject = ['$stateParams', '$state', '$uibModalInstance', '$scope', 'StripeService'];

function OfficialEventPayouts($stateParams, $state, $uibModalInstance, $scope, StripeService) {

    $scope.loading = {
        data_loaded: false,
        error: false,
        errMsg: ''
    };

    $scope.payouts = {
        history: [],
        total: 0,
        dashboardURL: '',
        connectionURL: ''
    };

    $scope.event = {
        id: $stateParams.event,
        name: $stateParams.name
    };

    init();

    function init() {
        StripeService.getOfficialStripeDashboardURL($scope.event.id)
            .then((res) => {
                $scope.payouts.dashboardURL = res.dashboardURL;
                if (res.dashboardURL) {
                    return StripeService.getPayoutsHistory($scope.event.id)
                        .then((res) => {
                            $scope.loading.data_loaded = true;
                            $scope.payouts.history = res.payouts_history;
                            $scope.payouts.total = res.total;
                        });
                } else {
                    return StripeService.generateExpressAcccountCreationLink($scope.event.id)
                        .then((res) => {
                            $scope.loading.data_loaded = true;
                            $scope.payouts.connectionURL = res.connectionURL;
                        });
                }
            })
            .catch(function (err) {
                $scope.loading.data_loaded = true;
                $scope.loading.error = true;
                $scope.loading.errMsg = (err.data && err.data.validation) ?
                    err.data.validation :
                    'Internal Server Error.'
            });
    };

};
