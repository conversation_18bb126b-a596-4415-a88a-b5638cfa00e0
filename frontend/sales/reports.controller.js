angular.module('SportWrench').controller('ReportsController', ReportsController);

ReportsController.$inject = ['$scope', '$http', '$state', 'APP_ROUTES', 'SharedService'];

function ReportsController ($scope, $http, $state, APP_ROUTES, SharedService) {
    $scope.events = [];

    generateSeasons();

    $scope.filterBySeason = function(item) {
        return Number($scope.season) === item.season;
    };

    $http.get('/api/sales/events/report')
    .then(function (resp) {
        $scope.events = resp.data.events;
    })

    $scope.show_details = function (id) {
        $state.go(APP_ROUTES.SM.REPORT_DETAILS, {
            event: id
        })
    }

    function generateSeasons() {
        SharedService.generateSeasons()
            .then(({ season, seasons }) => {
                $scope.season = season;
                $scope.seasons = seasons;
            })
    }
}
