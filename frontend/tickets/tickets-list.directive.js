angular.module('SportWrench').directive('ticketsList', ticketsList);

ticketsList.$inject = ['UserTicketsService'];

function ticketsList(UserTicketsService) {
    return {
        restrict: 'E',
        scope: {},
        templateUrl: 'tickets/tickets-list.html',
        link: function link (scope) {
            scope.purchaseData = {
                purchases: []
            };
            scope.utils = {
                loading: true
            }       

            scope.isWaitlisted = function (p) {
                return p.type === 'waitlist';
            }       

            scope.canChangeType = function (p) {
                return Boolean(p.typeChangeLink);
            }     

            UserTicketsService.getUserPurchases().success(function (data) {
                scope.purchaseData.purchases = data.purchases;
            }).finally(function () {
                scope.utils.loading = false;
            })
        }
    }
}


