
class Controller {
    constructor (PaymentCardService, toastr, $stateParams, STRIPE_PAYMENT_TYPE) {
        this.PaymentCardService = PaymentCardService;
        this.toastr = toastr;
        this.eventID = $stateParams.event;
        this.STRIPE_PAYMENT_TYPE = STRIPE_PAYMENT_TYPE;
    }

    get UNKNOWN_CARD_BRAND_ICON () {
        return 'fa-credit-card';
    }

    $onInit () {
        this.cards = [];
        this.isLoading = false;

        this.getCustomerPaymentMethods();
    }

    getLast4 (paymentMethod) {
        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.CARD) {
            return paymentMethod.card_last_4
        }

        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.ACH) {
            return paymentMethod.bank_account_last_4
        }

        return ''
    }

    getLabel (paymentMethod) {
        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.CARD) {
            return `${paymentMethod.card_exp_month}/${paymentMethod.card_exp_year}`
        }

        if(paymentMethod.type === this.STRIPE_PAYMENT_TYPE.ACH) {
            return paymentMethod.bank_name
        }

        return ''
    }

    setDefault (card) {
        if(card.is_default) {
            return;
        }

        this.PaymentCardService.setDefaultPaymentCard(card.stripe_payment_method_id)
            .then(() => {
                this.cards = this.cards.map(c => {
                    if(c.is_default && c.stripe_payment_method_id !== card.stripe_payment_method_id) {
                        c.is_default = false
                    }

                    if(c.stripe_payment_method_id === card.stripe_payment_method_id) {
                        c.is_default = true;
                    }

                    return c;
                });
            });
    }

    getCardBrandClass (cardBrand) {
        return this.PaymentCardService.getCardBrandClass(cardBrand);
    }

    getCustomerPaymentMethods () {
        this.isLoading = true;

        return this.PaymentCardService.getPaymentCards(this.eventID)
            .then(data => {
                if(!data || !data.paymentCards) {
                    this.cards = [];
                }

                this.cards = data.paymentCards;
            })
            .finally(() => this.isLoading = false);
    }

    removePaymentMethod (card) {
        if(card.disabledForRemove) {
            return;
        }

        card.disabledForRemove = true;

        return this.PaymentCardService.removePaymentCard(card.stripe_payment_method_id)
            .then(() => {
                this.cards = this.cards.filter(c => c.stripe_payment_method_id !== card.stripe_payment_method_id);

                this.toastr.success('Success');
            })
            .catch(err => {
                card.disabledForRemove = false;

                console.error(err);
            })
    }
}

Controller.$inject = ['PaymentCardService', 'toastr', '$stateParams', 'STRIPE_PAYMENT_TYPE'];

angular.module('SportWrench').component('paymentCardsList', {
    templateUrl: 'events/stripe/payment-card/payment-cards-list/template.html',
    controller: Controller
});
