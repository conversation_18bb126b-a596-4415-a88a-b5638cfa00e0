angular.module('SportWrench').service('campsService',
    ['$http', 'moment', '$uibModal', 'APP_ROUTES', '$state', campsService]
);

function campsService ($http, moment, $uibModal, APP_ROUTES, $state) {
	this._$http 			= $http;
    this._moment            = moment;
    this._$uibModal         = $uibModal;
    this._APP_ROUTES        = APP_ROUTES;
    this._$state            = $state;
	this._baseUrl 			= '/api/event/'; 
    this._dateProps         = [
        'date_start', 
        'date_end', 
        'age_date', 
        'purchase_start', 
        'purchase_end'
    ];
}

Object.defineProperty(campsService.prototype, 'CAMP_EDIT_MODE', {
    value 			: {
        EDIT    : 'edit',
        CREATE  : 'create'
    },
    writable 		: false,
    configurable 	: false
});

campsService.prototype.getAllCamps = function (eventId) {
    return this._$http.get(this._baseUrl + eventId + '/camps')
        .then(function (res) {
            return res.data;
        });
};

campsService.prototype.upsertCamp = function (eventId, camp) {
    
    for (let p of this._dateProps) {
        if (camp[p]) {
            camp[p] = this._moment(camp[p]).format('MM/DD/YYYY hh:mm a');
        }
    }

    return this._$http.put(this._baseUrl + eventId + '/camps' , { camp: camp })
    .then(function(res) {
        return res.data.camp;
    });
};

campsService.prototype.saveEventAgeDate = function (eventId, event_age_date) {
    return this._$http.put(this._baseUrl + eventId + '/camps/age-date ' , { event_age_date });
};

campsService.prototype.swapSortOrder = function(eventId, swapObj) {
    return this._$http.put(this._baseUrl + eventId + '/camps/swap-sort-order', { swapObj })
            .then((res) => {
                return res.data;
            });
};

campsService.prototype.delete = function (eventID, eventCampID) {
    return this._$http.delete(`${this._baseUrl}${eventID}/${eventCampID}`);
};

campsService.prototype.copy = function (eventID, eventCampID) {
    return this._$http.post(`${this._baseUrl}${eventID}/${eventCampID}/copy`)
        .then(response => response.data && response.data.camp);
};

campsService.prototype.openCampsModal = function (mode, camp) {
    let self = this;

    this._$uibModal.open({
        templateUrl: 'events/dashboard/camps/camps-upsert.html',
        controller : 'CampsUpsertController',
        resolve: {
            mode: function () {
                return mode;
            },
            camp: function () {
                return camp;
            }
        }
    }).result.then(function() {
        self._$state.transitionTo(self._APP_ROUTES.EO.CAMPS, null, {
            reload : true,
            inherit: true,
            notify : true
        });
    }, function() {
        self._$state.go(self._APP_ROUTES.EO.CAMPS);
    });
}
