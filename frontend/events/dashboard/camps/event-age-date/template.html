<div class="row" ng-if="!$ctrl.campsLoading">
    <div class="col-md-6 col-sm-8 col-xs-12">
        <div ng-class="{'panel panel-warning camp-panel': !$ctrl.eventAgeDate || !$ctrl.lastSavedAgeDate,
                        'panel panel-default camp-panel': $ctrl.eventAgeDate}">
            <div class="panel-heading camp-panel-heading">
                <h3 class="panel-title pull-left"
                    ng-if="!$ctrl.eventAgeDate || !$ctrl.lastSavedAgeDate"
                >
                    Fill in Event Age Date to start adding new Camps
                </h3>
                <h3 class="panel-title pull-left"
                    ng-if="$ctrl.eventAgeDate && $ctrl.lastSavedAgeDate"
                >
                    Camp Settings
                </h3>
                <div class="clearfix"></div>
            </div>
            <div ng-class="panel-body">
                <div class="row camp-panel-body">
                    <h3 class="col-sm-3 panel-title">Event Age Date</h3>
                    <div class="col-sm-4 no-clear-btn no-time-btn no-time-picker">
                        <div class="input-group date-time-control no-clear-btn"
                             ng-click="$ctrl.toggleDatePicker()"
                        >
                            <!-- ng-style="!last_saved_age_date && {'border':'solid 3px red', 'border-radius':'8px'}"  -->
                            <form name="$ctrl.form">
                                <input type="text"
                                       ng-model="$ctrl.eventAgeDate"
                                       class="form-control white-ro pointer"
                                       uib-datepicker-popup="MM/dd/yyyy"
                                       is-open="$ctrl.isOpen"
                                       ng-change="$ctrl.checkForAutoSave()"
                                       readonly
                                       required>
                            </form>
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        <p class="spacer-sm-t">
                            Set to:
                            <span ng-if="!$ctrl.isAgeDateEqualTo($ctrl.eventDateStart)">
                                <a
                                    href=""
                                    style="cursor:pointer;"
                                    ng-click="$ctrl.setEventAgeDateTo($ctrl.eventDateStart)"
                                >
                                    Event Start
                                </a>
                            </span>
                            <span ng-if="$ctrl.isNotEqual">or</span>
                            <span ng-if="!$ctrl.isAgeDateEqualTo($ctrl.defaultAgeDate)">
                                <a
                                    href=""
                                    style="cursor:pointer;"
                                    ng-click="$ctrl.setEventAgeDateTo($ctrl.defaultAgeDate)"
                                >
                                    Default
                                </a>
                            </span>
                        </p>
                    </div>
                    <div class="col-sm-2">
                        <button class="btn btn-success" ng-click="$ctrl.saveEventAgeDate(true)">Save</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
