angular.module('SportWrench').component('staffOnlineCheckinHistory', {
    templateUrl: 'events/dashboard/team-info/staffers/online-checkin-history/online-checkin-history.html',
    bindings: {
        onlineCheckinHistory: '<'
    },
    controller: Component,
});

Component.$inject = ['ONLINE_CHECKIN_HISTORY_ACTION_TYPE'];

function Component(ONLINE_CHECKIN_HISTORY_ACTION_TYPE) {
    this.textFormat = {
        [ONLINE_CHECKIN_HISTORY_ACTION_TYPE.SCAN]: 'Scanned on <%= created %>',
        [ONLINE_CHECKIN_HISTORY_ACTION_TYPE.REENTRY]: 'Re-entry for #<%= barcode %> on <%= created %>',
    }

    this.generateText = function (onlineCheckin) {
        const html = this.textFormat[onlineCheckin.action_type];
        const compiled = _.template(html);

        return compiled(onlineCheckin);
    }
}
