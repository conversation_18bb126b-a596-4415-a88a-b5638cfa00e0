angular.module('SportWrench').controller('Events.TeamInfo.TabPaymentsController', 
    function (
        $scope, toastr, eventPaymentsService, rosterTeamService, purchaseService, $http, $log, $stateParams, $rootScope,
        AllTeamsService,
) {
    
    $scope.loading = { 
        data_loaded: false,
        error: false,
        errMsg: ''
    };

    $scope.team = {
        payments: [],
        status_paid: null,
    }

    $scope.status = {
        opened: false
    }

    $scope.openPaymentMenu = false;

    $scope.togglePaymentMenu = function () {
        $scope.openPaymentMenu = !$scope.openPaymentMenu;
    }

    var rosterTeamID;

    var stopWatcher = $scope.$parent.$watch('tabs.team', function (value) {
        if(_.isEmpty(value)) return; 

        $log.debug('payments watcher call', value);

        rosterTeamID = value;

        _getPayments(value);
        stopWatcher();
    }, true);

    function _getPayments (team) {
        $scope.reg_method = $scope.$parent.event && $scope.$parent.event.registration_method;
        var _data = {};
        if($scope.reg_method === 'doubles') _data.team = team.roster_team_id;
        else _data.roster_club = team.roster_club_id;

        eventPaymentsService.eventPayments($scope.$parent.modalParams.event_id, _data)
            .then(function(resp) {
                $scope.team.payments = resp.data.payments;
                $scope.loading.data_loaded = true;
            })
            .catch(function(err) {
                $scope.loading.data_loaded = true;
                $scope.loading.error = true;
                err.data && err.data.validation
                    ? $scope.loading.errMsg = err.data.validation
                    : $scope.loading.errMsg = 'Internal Server Error.'
            });

    }

    function __paymentDataChanged() {
        $scope.$emit('team.info.data-changed');
    }

    $scope.paymentChanged = function (teams) {
        if(teams && teams.length) {
            __updateTeamsData(teams)
        } else {
            __paymentDataChanged();
        }
    };

    $scope.cancelPurchase = function (id, items) {
        return purchaseService.cancel($stateParams.event, id, items).then(function (res) {
            var data = res.data || {};
            var isFullCancellation = data.is_full;

            if (isFullCancellation) {

                _getPayments(rosterTeamID);
            }

            __paymentDataChanged();

            return data;
        })
    }

    $scope.newPaymentArrowClass = function () {
        return {
            'fa'                        : true,
            'fa-caret-square-o-down'    : $scope.openPaymentMenu,
            'fa-caret-square-o-right'   : !$scope.openPaymentMenu
        }
    }

    $scope.reloadPaymentsListOnPurchaseSave = function (teams) {

        $scope.paymentChanged(teams);

        _getPayments(rosterTeamID);
    }

    function __updateTeamsData(teams) {
        $rootScope.$emit('updateTeamsData', teams);
    }

    $scope.loadUnpaidTeams = function () {
        return rosterTeamService.getUnpaidTeams($stateParams.event, { roster_club: $scope.tabs.team.roster_club_id });
    }

    $scope.showMessageForTeamInFreeDivision = function () {
        const division_reg_fee = $scope.tabs.team && $scope.tabs.team.division_reg_fee;

        if (division_reg_fee === null) {
            return false;
        }

        return Number(division_reg_fee) === 0;
    }

    $scope.changePaidStatus = function (status, notes) {
        return AllTeamsService.changePaymentStatus($stateParams.event, {
            status, teams: [$scope.tabs.team.roster_team_id], notes
        }).then(() => {
            toastr.success('Saved');

            __paymentDataChanged();
        });
    }
})
