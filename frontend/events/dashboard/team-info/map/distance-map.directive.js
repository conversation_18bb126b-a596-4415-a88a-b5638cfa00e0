angular.module('SportWrench').directive('distanceMap', ['uiGmapGoogleMapApi', '$q', 'toastr', '$timeout', 'uiGmapIsReady',
    function (uiGmapGoogleMapApi, $q, toastr, $timeout, uiGmapIsReady) {
    var METERS_IN_MILE = 1609.344;
/*         COUNTRIES = {
            'AS': 'American Samoa',
            'BS': 'Bahamas',
            'CA': 'Canada',
            'GU': 'Guam',
            'ME': 'Mexico',
            'NA': 'Other',
            'PR': 'Puerto Rico',
            'US': 'United States',
            'VI': 'Virgin Islands'
        } */
    return {
        restrict: 'E',
        scope: {
            eventLoc    : '&', // promise
            clubLoc     : '&', // promise
            countries   : '<'
        },
        templateUrl: 'events/dashboard/team-info/map/distance-map.html',
        link: function (scope) {
            scope.map = {
                control: {},
                center: {
                    latitude: 0,
                    longitude: 0
                },
                bounds: {},
                zoom: 0
            };

            scope.eventMarker = {
                id: 'eventLoc',
                options: {
                    title: 'Event Location'
                }
            };

            scope.clubMarker = {
                id: 'clubLoc',
                options: {
                    title: 'Club Location'
                }
            }

            scope.utils = {
                initializingProgress: 0
            }

            __initializeMap();

            var map, geocoder

            function __initializeMap() {
                    uiGmapGoogleMapApi
                        .then(__setMapInstance)
                        .then(__loadMarkers)
                        .then(__createMarkersBounds)
                        .then(__fitBounds)
                        .then(__calculateDistance)
                        .catch(function (error) {
                            var msg = (_.isString(error)) ? error : error.validation;

                            if (msg) toastr.error(msg)
                        })
            }

            function __setMapInstance (maps) {
                map         = maps;
                geocoder    = new map.Geocoder();

                return $q.when(map)
            }

            function __updateMarker (geocoder, marker, locationObj) {
                var defer       = $q.defer(),
                    address     = marker.address || formatAddress(locationObj);

                if(!address) {
                    return defer.reject('Address not found')
                }

                geocoder.geocode({ address: address }, function (results) {
                    var locResult, latlng;

                    if(!results || !results.length) {
                        return defer.reject('Location for address "' + address + '" not found');
                    }

                    locResult       = _.first(results);
                    latlng          = locResult.geometry.location;

                    marker.address  = locResult.formatted_address;
                    marker.latlng   = latlng;
                    marker.coords   = { latitude: latlng.lat(), longitude: latlng.lng() };  

                    defer.resolve(latlng);
                });

                return defer.promise;
            }

            function __loadMarkers () {

                var eventBoundsPrimise = scope.eventLoc().then(function (locationObj) {
                    return __updateMarker(geocoder, scope.eventMarker, locationObj);
                });

                var clubBoundsPrimise = scope.clubLoc().then(function (locationObj) {
                    return __updateMarker(geocoder, scope.clubMarker, locationObj);
                })

                return $q.all([eventBoundsPrimise, clubBoundsPrimise]);
            }

            function __createMarkersBounds (latLngList) {
                return $q.resolve().then(function () {
                    var bounds = new map.LatLngBounds();

                    latLngList.forEach(function (latLng) {
                        bounds.extend(latLng);
                    })

                    return bounds;
                })
            }

            function formatAddress (data) {
                if(_.isEmpty(data)) return '';
                var addr = '';        
                if (data.address)   addr += data.address;
                if (data.city)      addr += ' ' + data.city;
                if (data.state)     addr += ' ' + data.state;
                if (data.zip)       addr += ' ' + data.zip;
                if (data.country)   addr += ' ' + scope.countries[data.country];
                return addr;
            }

            function __fitBounds (bounds) {

                scope.initMap = true;

                return uiGmapIsReady.promise(1).then(function () {

                    scope.map.control.getGMap().fitBounds(bounds);
                    scope.map.control.refresh();

                });
            }

            function __calculateDistance () {
                var distance = 
                    map.geometry.spherical.computeDistanceBetween(scope.eventMarker.latlng, scope.clubMarker.latlng);
          
                scope.map.distance = Math.round(distance / METERS_IN_MILE);

                return $q.when(distance);
            }

            scope.updateMap = function () {
                $q.all([
                    __updateMarker(geocoder, this.clubMarker),
                    __updateMarker(geocoder, this.eventMarker)
                ])
                .then(__createMarkersBounds)
                .then(__fitBounds)
                .then(__calculateDistance);
            }
        }
    }
}])
