angular.module('SportWrench').component('travelCoordinatorInfo', {
    templateUrl 	: 'events/dashboard/team-info/travel-coordinator-info/travel-coordinator-info.html',
    bindings 		: {
        data 	: '<'
    },
    controller 		: [TravelCoordinatorInfoController]
});

function TravelCoordinatorInfoController() {
    this.isEmptyData = function () {
        return !this.data.ths_trav_coord_name && !this.data.ths_trav_coord_email && !this.data.ths_trav_coord_phone;
    }
}
