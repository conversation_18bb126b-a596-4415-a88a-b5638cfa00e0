<div class="row-space"></div>

<button class="btn btn-primary" ng-click="$ctrl.openCreateModal()">Create contact list</button>

<div class="row-space"></div>

<spinner active="!$ctrl.dataLoaded"></spinner>
<uib-accordion ng-if="$ctrl.dataLoaded">
    <uib-accordion-group
        is-open="true"
        template-url="events/settings-accord-heading.html">

        <uib-accordion-heading>
            <span class="badge" ng-bind="$ctrl.lists.length"></span> Lists of recipients
        </uib-accordion-heading>

        <ul class="list-group tmpls-list" ng-if="$ctrl.lists.length">
            <li class="list-group-item pointer" ng-repeat="list in $ctrl.lists" ng-click="$ctrl.openList(list)">
                <mark ng-bind="$ctrl.getListScope(list)"></mark>
                <span class="text-bold" ng-bind="list.title"></span>
                <span class="pull-right text-bold" >{{list.email_count}} emails</span>
            </li>
        </ul>

        <uib-alert type="warning text-center" ng-if="!$ctrl.lists.length">No lists found</uib-alert>

    </uib-accordion-group>
</uib-accordion>
