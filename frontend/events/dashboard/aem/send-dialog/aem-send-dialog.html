<div class="modal-header text-center">
    <h4 ng-bind="::$ctrl.modalTitle"></h4>
</div>
<div class="modal-body">
    <form class="form-horizontal" name="$ctrl.emailForm">

        <div class="form-group">
            <label class="control-label col-sm-2">Template</label>
            <div class="col-sm-10">
                <select 
                    name="template"
                    ng-show="!$ctrl.tmplsLoading"
                    class="form-control"
                    ng-model="$ctrl.chosenTmpl"
                    ng-change="$ctrl.chooseTmpl()"
                    ng-options="t as t.title for t in $ctrl.templates">
                    <option value="">No Template ...</option>
                </select>
                <spinner active="$ctrl.tmplsLoading"></spinner>
            </div>
        </div>

        <div ng-class="{ 'validation-required form-group': true, 'has-error': $ctrl.isSubjectInvalid()}">
            <label class="control-label col-sm-2">Email Subject</label>
            <div class="col-sm-10">
                <input type="text"
                       class="form-control"
                       name="subject"
                       ng-model="$ctrl.email.subject"
                       required
                       ng-disabled="$ctrl.chosenTmpl"
                       ng-maxlength="$ctrl.MAX_EMAIL_SUBJECT_LENGTH">
            </div>
        </div>

        <div ng-if="$ctrl.replyTo" ng-class="$ctrl.genGroupCls('replyto')">
            <label class="control-label col-sm-2">Reply to</label>
            <div class="col-sm-10">
                <input type="text"
                       class="form-control"
                       name="replyto"
                       ng-model="$ctrl.email.replyto"
                       ng-model-options="{ updateOn: 'blur' }"
                       email-validator>
            </div>
        </div>

        <div ng-class="$ctrl.genGroupCls('cc')">
            <label class="control-label col-sm-2">Email cc</label>
            <div class="col-sm-10">
                <input type="text"
                       class="form-control"
                       name="cc"
                       ng-model="$ctrl.email.cc"
                       ng-model-options="{ updateOn: 'blur' }"
                       multiple-emails-validator>
            </div>
        </div>

        <div ng-class="$ctrl.genGroupCls('bcc')">
            <label class="control-label col-sm-2">Email bcc</label>
            <div class="col-sm-10">
                <input type="text"
                       class="form-control"
                       name="bcc"
                       ng-model="$ctrl.email.bcc"
                       ng-model-options="{ updateOn: 'blur' }"
                       multiple-emails-validator>
            </div>
        </div>

        <div class="form-group variables-block" ng-show="$ctrl.showVarsBlock()">
            <label class="control-label col-sm-2">Variables</label>
            <div class="col-sm-10">   
                <div class="help-block" ng-transclude="variables"></div>             
            </div>
        </div>

        <div class="form-group" ng-if="$ctrl.showRecipients()">
            <label class="control-label col-sm-2">Recipients</label>
            <div class="col-sm-3">
                <label class="checkbox-inline font-bold">
                    <input type="checkbox" ng-model="$ctrl.settings.send_to_club_director"> Club Directors
                </label>
            </div>
            <div class="col-sm-offset-1 col-sm-3">
                <label class="checkbox-inline font-bold">
                    <input type="checkbox" ng-model="$ctrl.settings.send_to_staff"> Team Staff
                </label>
            </div>
        </div>

        <div class="form-group" ng-if="$ctrl.showCDReceivers()">
            <label class="control-label col-sm-2">Recipients</label>
            <div class="col-sm-3">
                <label class="checkbox-inline font-bold">
                    <input type="checkbox" ng-model="$ctrl.settings.send_to_cd_email"> Club Directors Emails
                </label>
            </div>
            <div class="col-sm-offset-1 col-sm-3">
                <label class="checkbox-inline font-bold">
                    <input type="checkbox" ng-model="$ctrl.settings.send_to_cd_admin_email"> Administrative Emails
                </label>
            </div>
        </div>

        <div class="form-group" ng-if="$ctrl.showRecipients() && $ctrl.chosenTmpl">
            <div class="col-sm-offset-2 col-sm-10">
                <label class="checkbox-inline font-bold">
                    <input type="checkbox" ng-model="$ctrl.settings.not_notified"> Do not send to previous recipients
                </label>
            </div>
        </div>

        <div class="form-group" ng-if="$ctrl.showCKEditor()">
            <div class="col-sm-12">
                <textarea
                   ckeditor="ckeditorOptions"
                   ng-model="$ctrl.email.content"   
                   style="height: 0" 
                   name="content"
                   id="aem-send-dialog-editor"    
                   required
                   > {{$ctrl.email.content}}
               </textarea>
            </div>            
        </div>

    </form>    
</div>

<div class="modal-footer">
    <!--label class="checkbox-inline" ng-if="!$ctrl.hideSaveBtn">
        <input type="checkbox" ng-model="$ctrl.settings.save_changes"> Save template
    </label-->
    <button class="btn btn-success"
            ng-disabled="$ctrl.isSendingInProgress"
            ng-click="$ctrl.send()"
    >{{$ctrl.genSubmitBtnText()}}</button>
    <button class="btn btn-default" ng-click="$ctrl.closeModal()">Close</button>
</div>
