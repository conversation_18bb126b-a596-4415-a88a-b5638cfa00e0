angular.module('SportWrench').component('massSend', {
    templateUrl: 'events/dashboard/aem/mass-send/mass-send.html',
    bindings: {
        templateId: '<',
    },
    controller: ['AEMService', '$stateParams', 'toastr', '$rootScope',  Component]
});

function Component(AEMService, $stateParams, toastr, $rootScope) {
    this.events             = [];
    this.groups             = [];
    this.jobs               = [];
    this.sendingInProcess   = false;
    this.replyTo            = '';

    this.$onInit = function () {
        AEMService.getEvents($stateParams.event)
            .then(response => {
                const { data: { events, groups } } = response;

                this.events = events;
                this.groups = groups;
            });
    };

    this.showGroup = function (group) {
        if(!group.event_id) {
            return true;
        }

        return this.events.some(event => event.event_id === group.event_id && event.selected);
    };

    this.save = function() {
        let eventIds          = [];
        let selectedGroups    = [];

        _.forEach(this.events, (event) => {
            if (event.selected) {
                eventIds.push(event.event_id);
            }
        });

        _.forEach(this.groups, (group) => {
            if(group.selected) {
                // check event id selected for custom_list recipients
                if(group.group === 'custom_list' && (eventIds.includes(group.event_id) || !group.event_id)) {
                    selectedGroups.push(_.pick(group, ['list_id', 'group']));
                } else if(group.group !== 'custom_list') {
                    selectedGroups.push({group: group.group});
                }
            }
        });

        const data = {
            events      : eventIds,
            recipients  : selectedGroups,
            template_id : this.templateId,
            reply_to     : this.replyTo
        };

        if (!this.sendingInProcess) {
            this.sendingInProcess = true;

            AEMService.massSend(data).then(response => {
                this.sendingInProcess = false;

                if (response.status === 200) {
                    toastr.success('Emails successfully sent');

                    $rootScope.$broadcast('retrieveJobs');
                }
            })
        }
    };
}


