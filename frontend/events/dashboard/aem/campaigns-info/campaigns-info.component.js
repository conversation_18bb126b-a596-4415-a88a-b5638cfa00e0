angular.module('SportWrench').component('campaignsInfo', {
    templateUrl: 'events/dashboard/aem/campaigns-info/campaigns-info.html',
    bindings: {
        templateId: '<',
    },
    controller: ['AEMService', '$stateParams', '$rootScope', Component]
});

function Component(AEMService, $stateParams, $rootScope) {
    this.jobs = [];

    this.STATUSES = {
        scheduled: 'Pending',
        recipients_list_generation: 'Preparing Recipients List',
        sending_emails: 'Sending',
        done: 'Done',
    };

    this.$onInit = function() {
        this.retrieveJobs();
    };

    $rootScope.$on('retrieveJobs', () => {
        this.retrieveJobs();
    });

    const onRetrieveJobs = (response) => {
        const { data: { jobs } }  = response;

        this.jobs = jobs;
    };

    this.getStatus = function(status) {
        return this.STATUSES[status];
    };


    this.retrieveJobs = () => {
        AEMService.retrieveJobs($stateParams.event, this.templateId)
            .then(onRetrieveJobs);
    };

}


