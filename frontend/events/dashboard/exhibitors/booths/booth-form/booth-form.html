<div class="modal-header">
    <h2 class="text-info text-center">{{utils.isEditMode?'Update':'Create'}} Booth</h2>
</div>
<div class="modal-body">
    <form class="form-horizontal" name="boothForm" ng-submit="submit()">
        <div ng-class="{ 'form-group': true, 'has-error': boothForm.$submitted && boothForm.title.$invalid }">
            <label class="col-sm-4 control-label">Title</label>
            <div class="col-sm-6">
                <input name="title" type="text" class="form-control" placeholder="Title" ng-model="booth.title" required />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-4 control-label">Description</label>
            <div class="col-sm-6">
                <textarea name="description" class="form-control" placeholder="Description" ng-model="booth.description"></textarea>
            </div>
        </div>
        <div ng-class="{ 'form-group': true, 'has-error': boothForm.$submitted && boothForm.fee.$invalid }">
            <label class="col-sm-4 control-label">Fee</label>
            <div class="col-sm-6">
                <input name="fee" type="number" step="any" max="999999.99" min="0.1" class="form-control" ng-model="booth.fee" required />
            </div>
        </div>
        <div class="form-group">
            <label class="col-sm-4 control-label">Enabled</label>
            <div class="col-sm-6">
                <input name="is_enables" type="checkbox" ng-model="booth.is_enabled" />
            </div>
        </div>
        <div class="form-group">
            <div class="col-sm-offset-4">
                <button type="submit" class="btn btn-primary">Submit</button>
            </div>
        </div>
    </form>
</div>
<div class="modal-footer">
    <a class="btn btn-default" ng-click="$parent.$dismiss()">Cancel</a>
</div>
