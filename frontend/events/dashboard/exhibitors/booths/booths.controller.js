angular.module('SportWrench').controller('events.ExhibitorsController', ExhibitorsController);

ExhibitorsController.$inject = ['$scope', '$uibModal', '$stateParams', 'BoothService'];

function ExhibitorsController($scope, $uibModal, $stateParams, BoothService) {
            $scope.data = {
                booths: {},
            };

            function loadBooths () {
                BoothService.getAll($stateParams.event)
                    .then(function (resp) {
                        $scope.data.booths = resp.data;
                    });
            }

            loadBooths();

            $scope.createBooth = function() {
                openBoothModal();
            };

            $scope.editBooth = function (id) {
                openBoothModal(id);
            };

            function openBoothModal (boothId) {
                return $uibModal.open({
                    template: [
                        `<event-booth-form
                            ${ boothId ? (`booth-id="${boothId}"`) : '' }
                            event-id="${$stateParams.event}">
                        </event-booth-form>`
                    ].join(' ')
                }).result.then(function () {
                    loadBooths();
                });
            }

            $scope.removeBooth = function (id) {
                if(confirm('Are you sure you want to permanently remove this exhibitor item?')) {
                    BoothService.remove($stateParams.event, id).then(function () {
                        loadBooths();
                    });
                }
            };
        }
