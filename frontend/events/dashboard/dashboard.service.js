angular.module('SportWrench').service('eventDashboardService', ['$q', eventDashboardService]);

function eventDashboardService($q) {
    this.event = {};
    this.eventDefer = $q.defer();
}

eventDashboardService.prototype.setEvent = function (event) {
    this.event = event;
    this.eventDefer.resolve(event);
};

eventDashboardService.prototype.getEvent = function () {
    if(!this.event || !this.event.event_id) {
        return this.eventDefer.promise;
    }
    return this.event;
};

eventDashboardService.prototype.updateEvent = function (data) {
    const keys = Object.keys(data);
    const self = this;
    keys.forEach(function (key) {
        self.event[key] = data[key];
    });
};
