<div class="form-inline">
	<table class="table table-condensed p-sm">
	    <thead>
	        <tr>
	            <th></th>
	            <th>Purchased</th>
	        </tr>
	    </thead>
	    <tbody>
	        <tr ng-repeat-start="t in ticketsList">
	            <td colspan="3">{{itemLabel(t)}}</td>
	        </tr>
	        <tr ng-repeat-end class="remove-border">
	            <td>
	                <label>Count:</label>
	                <select
	                    class="form-control form-control-select-small"
	                    ng-model="t.quantity"
	                    ng-change="countTotal()"
	                    ng-options="n as n for n in getRefundOptions(t.purchase_ticket_id)"
	                ></select>
	            </td>
	            <td ng-if="isDiscountMode(t)">
	                <label>Discount:</label>
	                <div class="input-group">
	                    <span class="input-group-addon form-control-input-small"><i class="fa fa-usd"></i></span>
	                    <input 
	                        type="text" 
	                        class="form-control form-control-input-small" 
	                        ng-model="t.discount"
							ng-change="countTotal()"
							float-number>
	                </div>
	            </td>
	            <td ng-if="isCancellationMode(t)">
	            	<label>Cancellation:</label>
	            	<div class="input-group">
	                    <span class="input-group-addon form-control-input-small"><i class="fa fa-usd"></i></span>
	                    <input 
	                        type="text" 
	                        min="0"
	                        class="form-control form-control-input-small" 
	                        ng-model="t.cancellation"
							ng-change="countTotal()"
							float-number>
	                </div>
	            </td>
	            <td><label>Subtotal: </label>{{getSubtotal(t.amount)}}</td>
	        </tr>
	    </tbody>
	</table>
</div>
<div class="form-horizontal">
	<div class="row text-right">
		<div class="col-xs-offset-4 col-xs-5">
			<strong>SW Fee:</strong>
		</div>
		<div class="col-xs-3">{{refund.sw_fee | currency}}</div>
	</div>
	<div class="row text-right">
		<div class="col-xs-offset-4 col-xs-5">
			<strong ng-if="!isTilledProvider">Stripe Fee:</strong>
			<strong ng-if="isTilledProvider">Tilled Fee:</strong>
		</div>
		<div class="col-xs-3">{{refund.provider_fee | currency}}</div>
	</div>
	<div class="row text-right">
		<div class="col-xs-offset-4 col-xs-5">
			<strong>Current Total:</strong>
		</div>
		<div class="col-xs-3">{{getCurrentAmount() | currency}}</div>
	</div>
	<hr class="m0" ng-if="amountChanged()" />
	<div class="row text-right" ng-if="amountChanged()">
		<div class="col-xs-offset-4 col-xs-5">
			<strong>Amount to refund:</strong>
		</div>
		<div class="col-xs-3">{{utils.amountToRefund | currency}}</div>
	</div>
	<div class="row text-right" ng-if="amountChanged()">
		<div class="col-xs-offset-4 col-xs-5">
			<strong>New Total:</strong>
		</div>
		<div class="col-xs-3">{{refund.total | currency}}</div>
	</div>
    <div class="form-group">
    	<label class="control-label col-sm-2">Notes:</label>
    	<div class="col-sm-10">
    		<textarea class="form-control" ng-model="refund.notes" rows="2"></textarea>
    	</div>
    </div>
    <div class="form-group">
    	<div class="col-sm-offset-7 col-sm-5">
	        <spinner active="utils.inProgress"></spinner>
	        <button class="btn btn-primary" ng-if="!utils.inProgress" ng-disabled="refundDisabled()" ng-click="processRefund()">Save</button>
	        <button class="btn btn-default" ng-if="!utils.inProgress" ng-click="disable()">Cancel</button>
        </div>
    </div>
    <div class="alert-block"><result-alert type="resultAlert.type" msg="resultAlert.msg"></result-alert></div> 
</div>
