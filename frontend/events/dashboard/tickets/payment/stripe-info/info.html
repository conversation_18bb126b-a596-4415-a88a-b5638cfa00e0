<div class="row rowm0 row-space">
	<div class="col-sm-12">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">General</h4>
			</div>
			<div class="panel-body">
				<div class="row">
					<div class="col-xs-12">{{(payment.source === 'api')?'On Site Purchase':'Online Purchase'}}</div>
				</div>
				<div class="row">
					<div class="col-xs-12">{{utils.title[payment.sales_type]}}</div>
				</div>
				<div ng-if="getTicketsInfo()" class="row">
					<div class="col-xs-12">
                        <span>{{getTicketsInfo()}}</span>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-12" ng-bind="payment.event_name"></div>
				</div>
				<div class="row">
					<div class="col-xs-12">Event Start: {{payment.event_date}}. {{payment.event_location}}</div>
				</div>
			</div>
		</div>
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">Stripe</h4>
			</div>
			<div class="panel-body">
				<div class="row">
					<div class="col-xs-2 font-bold">Charge Id:</div>
					<div class="col-xs-10">
						<a href="https://dashboard.stripe.com/search?query={{payment.purchaser_info.stripe_info.charge_id}}" target="_blank">{{payment.purchaser_info.stripe_info.charge_id}}</a>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-2 font-bold">Card Id:</div>
					<div class="col-xs-10">
						<a href="https://dashboard.stripe.com/search?query={{payment.purchaser_info.stripe_info.card_id}}" target="_blank">{{payment.purchaser_info.stripe_info.card_id}}</a>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-2 font-bold">Fingerprint:</div>
					<div class="col-xs-10">
						<a href="https://dashboard.stripe.com/search?query={{payment.purchaser_info.stripe_info.fingerprint}}" target="_blank">{{payment.purchaser_info.stripe_info.fingerprint}}</a>
					</div>
				</div>
			</div>
		</div>
        <div class="panel panel-default" ng-if="payment.dispute_created">
            <div class="panel-heading">
                <h4 class="panel-title">Send Email</h4>
            </div>
            <div class="panel-body">
                <ticket-stripe-email-form payment="payment"></ticket-stripe-email-form>
            </div>
        </div>
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">Activity Log</h4>
			</div>
			<div class="panel-body">
				<div class="row">
					<div class="col-xs-2 font-bold">Purchased</div>
					<div class="col-xs-10">{{payment.purchased}} <span ng-if="payment.source === 'api'" class="text-danger">via API</span></div>
				</div>
				<div class="row">
					<div class="col-xs-12 font-bold">Scanning log:</div>
				</div>
				<div class="row">
					<div class="col-xs-12" ng-bind-html="payment.history || 'N/A'"></div>
				</div>
			</div>
		</div>
		<div class="panel panel-default" ng-if="hasGodRole() && payment.purchaser_info.stripe_info.fingerprint">
			<div class="panel-heading">
				<h4 class="panel-title">Charges for fingerprint "{{payment.purchaser_info.stripe_info.fingerprint}}"</h4>
			</div>
			<div class="panel-body">
				<uib-alert type="warning text-center" ng-if="!utils.chargesLoading && (data.fCharges.length === 0)"></uib-alert>
				<spinner active="utils.chargesLoading"></spinner>
				<table class="table table-condensed" ng-if="!utils.chargesLoading && !!data.fCharges.length">
					<thead>
						<tr>
							<th>Created</th>
							<th>Event</th>
							<th>Amount</th>
							<th>Type</th>
							<th>Status</th>
						</tr>
					</thead>
					<tbody>
						<tr ng-repeat="p in data.fCharges">
							<td ng-bind="p.created"></td>
							<td ng-bind="p.event_name"></td>
							<td ng-bind="getAmount(p) | currency"></td>
							<td ng-bind="p.payment_for"></td>
							<td ng-bind="p.status"></td>
						</tr>
					</tbody>
				</table>
			</div>
		</div>
	</div>
</div>
