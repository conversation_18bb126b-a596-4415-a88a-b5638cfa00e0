<div ng-class="{'panel': true, 'panel-info': !utils.partialRefundEnabled, 'panel-warning': utils.partialRefundEnabled}">
    <div class="panel-heading">
        <span ng-if="!utils.partialRefundEnabled">Tickets Count</span>
        <span ng-if="utils.partialRefundEnabled"><i class="fa fa-exclamation-triangle"></i> Partial refund mode enabled <i class="fa fa-times pointer pull-right" ng-click="togglePartialRefundMode()"></i></span>
    </div>
    <div class="panel-body" ng-if="!utils.partialRefundEnabled" id="test">
        <dl class="dl-horizontal">
            <dt>Payment Method:</dt>
            <dd>
                {{ payment.purchaser_info.is_kiosk_payment ? 'Pay at Ticket Booth' : payment.type }}
                <a href="{{payment.typeChangeLink}}" target="_blank" rel="nofollow noopener" ng-if="isPendingCheck()">Pay by Card</a>
            </dd>

            <dt>Status:</dt>
            <dd>
                <payment-status-label payment="payment"></payment-status-label>
            </dd>

            <dt ng-if="payment.dispute_status !== null">Dispute Status:</dt>
            <dd ng-if="payment.dispute_status !== null">
                <payment-dispute-status-label payment="payment"></payment-dispute-status-label>
            </dd>

            <dt ng-if="isReceivedCheck()">Check received:</dt>
            <dd ng-if="isReceivedCheck()">{{payment.received_date}}</dd>

            <dt ng-if="payment.date_refunded">Last refunded:</dt>
            <dd ng-if="payment.date_refunded">{{payment.date_refunded}}</dd>

            <dt ng-if="payment.dispute_created">Dispute created:</dt>
            <dd ng-if="payment.dispute_created">{{payment.dispute_created}}</dd>

            <dt ng-if="payment.canceled_date">Canceled:</dt>
            <dd ng-if="payment.canceled_date">{{payment.canceled_date}}</dd>
        </dl>
        <refund-items-list
            list="payment.tickets"
            scan-available="payment.scan_available"
            type="{{payment.sales_type}}"
            show-cancel-participation-btn="showCancelParticipationBtn()"
            cancel-participation="cancelParticipation(camp)"
        ></refund-items-list>
        <div class="form-horizontal">
            <div class="form-group" ng-if="!showReceiveCheckBtn()">
                <div ng-if="showFees()">
                    <label ng-class="{ 'control-label': true, 'col-sm-7': (payment.status !== 'canceled'), 'col-sm-12': (payment.status === 'canceled')} ">Total Amount: {{getTotalAmount() | currency:'$'}}</label>
                    <label ng-class="{ 'control-label': true, 'col-sm-7': (payment.status !== 'canceled'), 'col-sm-12': (payment.status === 'canceled')} ">{{getSwFeeLabel()}}: {{payment.collected_sw_fee | currency:'$'}}</label>
                    <label ng-class="{ 'control-label': true, 'col-sm-7': (payment.status !== 'canceled'), 'col-sm-12': (payment.status === 'canceled')} ">Merchant Fee: {{payment.stripe_fee | currency:'$'}}</label>
                </div>
                <label ng-class="{ 'control-label': true, 'col-sm-7': (payment.status !== 'canceled'), 'col-sm-12': (payment.status === 'canceled')} ">Total Price: {{payment.amount_formatted}}</label>
                <div id="refund-type" class="btn-group" uib-dropdown ng-if="showRefundBtn()">
                    <button type="button" class="btn btn-primary" uib-dropdown-toggle> Refund <span class="caret"></span>
                    </button>   
                    <ul class="dropdown-menu" role="menu" aria-labelledby="refund-type">
                        <li role="menuitem">
                            <a href="" sw-confirm="Confirm a Full Refund" sw-confirm-do="fullRefund" sw-confirm-hide-no>Make A Full Refund</a>
                        </li>
                        <li role="menuitem"><a href="" ng-click="togglePartialRefundMode()">Partial Refund</a></li>
                    </ul>                     
                </div>
                <button
                    class="btn btn-danger"
                    sw-confirm="{{utils.cancellationText}}"
                    sw-confirm-do="voidPayment"
                    ng-if="showVoidBtn()"
                    sw-confirm-params="utils.confirmParams"
                    sw-confirm-hide-no
                >Void</button>
            </div>
            <check-receiver ng-if="showReceiveCheckBtn()" title="Check Received" action="saveCheck(data, done)">
                <label class="col-sm-5 control-label">Total Price: {{payment.amount_formatted}}</label>
                <button 
                    class="btn btn-danger" 
                    sw-confirm="{{utils.cancellationText}}" 
                    sw-confirm-do="voidPayment" 
                    ng-if="showVoidBtn()"
                    sw-confirm-params="utils.confirmParams"
                    sw-confirm-hide-no
                >Void</button>
            </check-receiver>
            <tickets-payment-debt 
                amount="payment.debt" 
                save-check="payOffDebt(data, done)"
                refund-debt="debtRefund()"
                ng-if="payment.debt"
            ></tickets-payment-debt>
        </div>
        <p class="lead" ng-if="payment.debt_history">Debt History:</p>
        <table class="table table-condensed" ng-if="payment.debt_history">
            <thead>
                <tr>
                    <th></th>
                    <th>Created</th>
                    <th>Check Num</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat-start="h in payment.debt_history">
                    <td>
                        <i ng-class="{ 'fa': true, 'fa-arrow-circle-down text-success': h.is_income, 'fa-arrow-circle-up text-danger': !h.is_income }"></i>
                    </td>
                    <td ng-bind="h.op_date"></td>
                    <td ng-bind="h.check_num"></td>
                    <td ng-bind="h.amount | currency"></td>
                </tr>
                <tr ng-repeat-end ng-if="h.notes" class="remove-border">
                    <td colspan="4"><b>Notes:</b> <em ng-bind="h.notes"></em></td>
                </tr>
            </tbody>
        </table>
    </div>
    <div class="panel-body" ng-if="utils.partialRefundEnabled">
        <tickets-partial-refund
            payment="payment"
            tickets="ticketsCopy()"
            disable="togglePartialRefundMode()"
            on-refund-finished="reloadPayment()">
        </tickets-partial-refund>
    </div>
    <div class="alert-block">
      <result-alert
          type="resultAlert.type"
          msg="resultAlert.msg">
      </result-alert>
    </div>                
</div>
