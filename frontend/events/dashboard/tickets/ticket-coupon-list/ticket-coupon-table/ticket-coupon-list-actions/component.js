
class Controller {
    constructor (ActionsService) {
        this.ActionsService = ActionsService;
    }

    openChangeTicketsCountModal () {
        let filters = this.getFiltersValues();

        this.ActionsService.openChangeTicketsCountModal(filters)
            .then(() => this.reloadCoupons());
    }

    openSendingCouponsModal () {
        let filters = this.getFiltersValues();

        this.ActionsService.openSendingCouponsModal(filters);
    }

}

Controller.$inject = ['TicketCouponListActionsService'];

angular.module('SportWrench').component('ticketCouponTableActions', {
    templateUrl: 'events/dashboard/tickets/ticket-coupon-list/ticket-coupon-table/ticket-coupon-list-actions/template.html',
    bindings: {
        getFiltersValues: '&',
        reloadCoupons: '&'
    },
    controller: Controller,
});
