angular.module('SportWrench').directive('campChange', campChange)

function campChange (ticketsService, $stateParams, $filter, StripeFeeService, UtilsService, FEE_PAYER) {
	return {
		restrict: 'E',
		scope: {
			receipt	: '=',
			price 	: '=',
			payment : '='
		},
		require: '^paymentModal',
		templateUrl: 'events/dashboard/tickets/type-change/camp-change.html',
		link: function (scope, elem, attrs, ctrl) {
			let filter 	      = $filter('filter');
			let buyerPaysFees = scope.payment.sw_fee_payer      === FEE_PAYER.BUYER ||
                                scope.payment.stripe_fee_payer  === FEE_PAYER.BUYER;

            scope.initialTotal      = Number(buyerPaysFees ? scope.payment.net_profit : scope.payment.amount);
			scope.campTypes 		= [];
			scope.receiptChanges 	= {};
			scope.resultAlert 		= {};

			scope.data = {
				debt 	: scope.payment.debt,
				total 	: scope.initialTotal
			};

			ticketsService.getCampsTypes($stateParams.event)
			.then(function (types) {
				scope.campTypes = types;
			});

			scope.label = function (type) {
				return (type.type_label + ' - ' + type.price_formatted)
			};

			scope.filterTypes = function (currentType) {
				return filter(scope.campTypes, function (typeValue) {
					return (typeValue.event_ticket_id !== currentType.event_ticket_id);
				})
			};

			scope.recountPrice = function () {
				let totalAmount = 0;

				scope.receipt.forEach(function (campType) {
					if(campType.quantity > 0) {
						let newType = scope.receiptChanges[campType.purchase_ticket_id];

						if(newType) {
							totalAmount += newType.current_price;
						} else {
							totalAmount += campType.ticket_price;
						}
					}
				});

                totalAmount         = UtilsService.approxNumber(totalAmount);
                let paymentAmount   = scope.initialTotal + (scope.payment.debt * -1);
                let debt            = UtilsService.approxNumber(totalAmount - paymentAmount);

                scope.data.debt	 = UtilsService.approxNumber(
                    Math.abs(debt) === Math.abs(scope.payment.debt) ? scope.payment.debt : debt
                );

                scope.data.total = totalAmount;
			};

			scope.save = function () {
				if(_.isEmpty(scope.receiptChanges))
					return;
				
				var changes = _.mapObject(scope.receiptChanges, function (ticketType) {
				  	return ticketType.event_ticket_id
				});

				ticketsService.changeCampType(scope.payment.event_id, scope.payment.barcode, changes, scope.data.debt)
				.then(function () {
					ctrl.reloadPayment(true);
					scope.resultAlert.msg = 'Successful change';
					scope.resultAlert.type = null;
				}, function (resp) {
					if(resp && resp.data.validation) {
						scope.resultAlert.msg = resp.data.validation;
						scope.resultAlert.type = 'danger'
					}
				})
			}

			scope.debtorName = function () {
				if(scope.data.debt < 0) {
					return 'Event Owner\'s Debt';
				} else if(scope.data.debt > 0) {
					return 'Buyer\'s Debt';
				} else {
					return 'Debt';
				}
			}

		}
	}
}
