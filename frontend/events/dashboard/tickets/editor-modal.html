<div class="modal-header">
    <p class="lead" ng-bind="::title"></p>
</div>
<div class="row rowm0 row-space">
    <div class="col-xs-12">        
        <textarea
            ckeditor="options" 
            ng-model="data"   
            ng-model-options="{ debounce: 500 }"
            style="height: 0" 
            >
        </textarea>
        <p class="text-center" ng-if="!isLoaded">
            <i class="fa fa-spinner fa-pulse fa-3x"></i>
        </p>
    </div>
</div>
<uib-alert type="danger" ng-if="error">{{error}}</uib-alert>
<div class="modal-footer">
    <uib-alert template-url="confirm-alert.html" ng-if="confirm.show">
        <div>Do you really want to discard all changes?</div>
        <div>
            <button class="btn btn-default" ng-click="confirmAgree()">Yes</button>
            <button class="btn btn-default" ng-click="confirmCancel()">No</button>
        </div>
    </uib-alert>
    <button class="btn btn-success" ng-click="saveChanges()" ng-disabled="disableSave()">Save</button>
    <button class="btn btn-warning" ng-click="discardChanges()">Undo</button>
    <button class="btn btn-default" ng-click="discardAndClose()">Undo & Close</button>
</div>


<script type="text/ng-template" id="confirm-alert.html">
    <div class="alert confirm-alert bottom" role="alert">
      <div ng-transclude></div>      
    </div>
  </script>
