<div
    class="editable-cell"
    ng-class="{
        'inline-block block-label': !priceAndWlType() && isLabel(),
        'inline-block block-short-label overflow-text': !priceAndWlType() && isShortLabel(),
        'inline-block block-md': !priceAndWlType() && isNotLabel(),
        'block-lg': priceAndWlType(),
        'border-color': isBorderColor(),
        }"
    >
    <select
        ng-if="elType === 'select' && type === 'ticket-type'"
        class="form-control select-sm"
        ng-show="isEditing"
        ng-model="utils.val"
        ng-change="onChange()"
    >
        <option ng-if="!isNotEmptyValue(utils.val)" value="">Select a Type</option>
        <option value="daily">Daily</option>
        <option value="weekend">Weekend</option>
    </select>
    <select
        ng-if="elType === 'select' && type === 'border-color'"
        class="form-control select-sm"
        ng-show="isEditing"
        ng-model="utils.val"
        ng-change="onChange()"
    >
        <option value="">Without border</option>
        <option ng-repeat="color in ticketBarcodeBackgroundColor" value="{{color.value}}">{{color.title}}</option>
    </select>
    <select
        ng-if="elType === 'select' && type === 'merchandise-type'"
        class="form-control select-sm"
        style="width: 200px;"
        ng-show="isEditing"
        ng-model="utils.val"
        ng-change="onChange()"
    >
        <option value="">Select Merchandise Type</option>
        <option ng-repeat="type in merchandiseTypes" value="{{type.value}}">{{type.title}}</option>
    </select>
    <select
        ng-if="elType === 'select' && type === 'sub-type'"
        class="form-control select-sm"
        ng-show="isEditing"
        ng-model="utils.val"
        ng-change="onChange()"
    >
        <option value="default">Default</option>
        <option value="parking">Parking</option>
    </select>
    <input
        ng-if="elType !== 'select' && elType !== 'checkbox'"
        type="{{::inputType}}"
        ng-model="utils.val"
        class="form-control input-sm"
        ng-class="{'w-sm pull-right': priceType(), 'w-lg': !priceType()}"
        placeholder="{{getPlaceholder()}}"
        ng-show="isEditing"
        ng-change="onChange()"
        null-value
    >
    <input
        ng-if="elType === 'checkbox'"
        class="checkbox w-sm pull-right"
        type="checkbox"
        ng-show="isEditing"
        ng-model="utils.val"
        ng-disabled="!isEditing"
        ng-change="onChange()"
    >
    <span
        ng-if="!isEditing"
        ng-click="enableEditing()" 
        class="pointer" 
        ng-class="{
            'edit-text': showEditIcon(), 
            'text-grey text-sm': ( (!isNotEmptyValue(val) && prevPrice) || (type === 'title-sm') ),
            'offset-sm': (priceAndWlType() && !isNotEmptyValue(val) && prevPrice),
            'offset-lg': (priceAndWlType() && isNotEmptyValue(val))
        }">
        <span class="glyphicon glyphicon-chevron-left text-grey" ng-show="!isNotEmptyValue(val) && prevPrice"></span>&nbsp;
        {{getValue()}}&nbsp;<i class="fa fa-pencil-square-o" ng-if="disabled != 'true'"></i>
    </span>
 </div>

