
class Controller {
    constructor (FreeTicketService, $stateParams, toastr) {
        this.service = FreeTicketService;
        this.$toastr = toastr;

        this.eventID = $stateParams.event;
        this.freeTicketInfo = {};
        this.freeTicketForm = null;
        this.EMAIL_PATTERN = /^\w+(?:[._+-]\w+)*@\w+(?:[_.-]\w+)*\.[a-zA-Z]{2,4}$/;

        this.loading = {
            inProcess: false,
        }
    }

    submit() {
        this.freeTicketForm.$setSubmitted();

        if (this.freeTicketForm.$invalid || this.loading.inProcess) {
            return;
        }

        this.loading.inProcess = true;

        const _data = Object.assign({}, this.freeTicketInfo, { ticket_type: this.ticketType });

        this.service.create(_data, this.eventID)
            .then(() => {
                this.$toastr.success('VIP Ticket Successfully Generated');

                this.onClose();
            })
            .finally(() => {
                this.loading.inProcess = false;
            })
    }

    getTicketTypeLabel() {
        const types = {
            'daily': 'Daily',
            'weekend': 'Weekend',
        }

        return types[this.ticketType];
    }
}

Controller.$inject = ['FreeTicketService', '$stateParams', 'toastr'];

angular.module('SportWrench').component('freeTicketForm', {
    templateUrl: 'events/dashboard/tickets/free-ticket/free-ticket-form/template.html',
    bindings: {
        onClose: '&',
        ticketType: '<',
    },
    controller: Controller,
})
