angular.module('SportWrench').factory('HousingStatusFactory', function () {
    return {
        getItems: function () {
            return [
                {
                    id: 31,
                    name: 'None',
                    class: 'glyphicon glyphicon-remove-circle red',
                }, {
                    id: 32,
                    name: 'Verified',
                    class: 'glyphicon glyphicon-ok-sign green',
                }, {
                    id: 33,
                    name: 'Issued',
                    class: 'glyphicon glyphicon-minus-sign blue',
                }, {
                    id: 34,
                    name: 'Below',
                    class: 'glyphicon glyphicon-exclamation-sign yellow-waiting',
                }, {
                    id: 35,
                    name: 'Faulty',
                    class: 'glyphicon glyphicon-ban-circle yellow-faulty'
                }, {
                    id: 'local',
                    name: 'Is Local',
                    class: 'fa fa-home'
                }, {
                    id: 'loyalty',
                    name: 'Is Loyalty',
                    class: 'fa fa-handshake-o'
                }, {
                    id: 'localDistance',
                    name: 'Local Distance',
                    class: 'fa fa-home',

                }, {
                    id: 'withoutDistance',
                    name: 'Teams without Distance',
                    class: 'fa fa-home',

                }
            ];
        },
        getItemsForChange() {
            return this.getItems()
                .filter(i=>_.isNumber(i.id))
                .sort(
                    (a,b)=> {
                        // Move "None" to the last position
                        if(a.id === 31) {
                            return 1;
                        }
                        if(b.id === 31) {
                            return -1;
                        }
                        // Everything else is sorted by numeric id
                        return a.id - b.id;
                    }
                )
        }
    }
})
