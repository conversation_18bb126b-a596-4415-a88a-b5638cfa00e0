angular.module('SportWrench').controller('Event.Tabs.AllTeamsController', AllTeamsController);


function AllTeamsController ( 
    $scope, ngTableParams, $log, $stateParams, divisionsService, $timeout, EntryStatusFactory, 
    PaymentStatusFactory, HousingStatusFactory, $sce, APP_ROUTES, lastUsedSortOrder, eventStrorage, _,
    eventDashboardService, MembersStatusFactory, AllTeamsService, TeamsDynamicColsFactory, $rootScope, $location,
    PAYMENT_TYPE, PAYMENT_STATUS, DB_DATETIME_FORMAT, MONTH_DAY_DATETIME_FORMAT
) {
    $scope.params   = {};
    $scope.data     = {
        teams               : [],
        checkboxes          : { 'checked': false, has_selections: false, items: {}, qty: 0 },
        total               : 0,
        divisions           : [],
        entry_statuses      : EntryStatusFactory.getItems(),
        payment_statuses    : PaymentStatusFactory.getItems(),
        housing_statuses    : HousingStatusFactory.getItems(),
        members             : MembersStatusFactory.getItems(
            _rosterMincount.bind(null, eventDashboardService.getEvent()),
            eventDashboardService.getEvent()
        )
    };
    $scope.filters = {
        limit   : 100,
        page    : 1
    };

    $scope.defaultSort = false;

    $scope.states = {
        add: APP_ROUTES.EO.ADD_TEAM
    };

    $scope.eventID = $stateParams.event;

    $scope.hasRosters = eventDashboardService.getEvent().has_rosters;

    $scope.hasStatusHousing = eventDashboardService.getEvent().has_status_housing;

    $scope.canAddTeamsManually = eventDashboardService.getEvent().is_with_manual_teams_addition;

    $scope.hasManualClubNames = eventDashboardService.getEvent().has_manual_club_names;

    $scope.hasCoupons = eventDashboardService.getEvent().require_coupon;

    if($stateParams.mode && $stateParams.value) {
        switch($stateParams.mode) {
            case 'division':
                $scope.filters.division = [$stateParams.value];                
                break;
            default: 
                break;
        }
    }
    // additional for filters
    $scope.utils = {};

    var loadLastUsedSorting = function (lastSortOrder) {
        if(_.isEmpty(lastSortOrder)) {
            return {};
        } else {
            var sorting = {};
            sorting[lastSortOrder.field] = lastSortOrder.reverse?'desc':'asc';
            return sorting;
        }
    };

    var reloadAll = false, limitOverride = null;

    var getData = function ($defer, params) {

        $log.log('getData trigger', reloadAll);

        var urlQuery = {};

        var filter = params.filter();

        var orderBy = params.orderBy();

        if (filter) {
            urlQuery = _.clone(filter);
        }

        if(orderBy && orderBy.length && !$scope.defaultSort) {
            urlQuery.order      = orderBy[0].substr(1);
            urlQuery.revert     = (orderBy[0].charAt(0) === '-');

            eventStrorage.setTeamsSorting(urlQuery.order, urlQuery.revert);
        }

        if (limitOverride !== null) {
            urlQuery.limit = limitOverride;
        }

        clearSelection();

        AllTeamsService.loadTeams($stateParams.event, urlQuery).then(function (resp) {
            $scope.data.total = resp.data.count;
            $scope.data.teams = reloadAll?resp.data.teams:$scope.data.teams.concat(resp.data.teams);

            $defer.resolve($scope.data.teams); 

            if($scope.data.checkboxes.checked) {
                pickLoadedTeams();
            }
        }, function () {
            params.total(0);
            $scope.data.teams = [];
            $defer.resolve([]);
        }).then(function (data) {
            reloadAll       = false;

            $scope.defaultSort = false;

            if (limitOverride !== null) {
                limitOverride = null;
            } else {
                $scope.utils.allPagesEnabled = false;
            }
            
            $timeout(function () {
                isLoadingFinished = true;
            }, 100);

            return data;
        });
    };

    var reloadData = function () {
        reloadAll = true;
        if($scope.filters.page !== 1) {
            $scope.filters.page = 1;
        } else {
            $scope.teams_table.reload();
        }
    };

    $scope.loadAllPagesTeams = function () {
        limitOverride = 0;
        reloadData();
        $scope.utils.allPagesEnabled = true;
    };

    $scope.reloadTeams = function () {
        reloadData();

        $scope.data.checkboxes.has_selections = false;
        $scope.data.checkboxes.checked = false;
    };

    $scope.teams_table = new ngTableParams({
        page        : 1,       
        count       : 1, // per page
        filter      : $scope.filters,
        sorting     : loadLastUsedSorting(lastUsedSortOrder)
    }, {
        total       : 0,
        counts      : [],
        filterDelay : 0,
        getData     : getData
    });

    var teamsTable          = $scope.teams_table,
        isNotDoublesEvent   = (eventDashboardService.getEvent().registration_method !== 'doubles');

    $scope.hasClubs   = eventDashboardService.getEvent().has_clubs;
    $scope.isDoubles = !isNotDoublesEvent;

    $scope.dynamic_columns = TeamsDynamicColsFactory.getColumns(isNotDoublesEvent, eventDashboardService.getEvent());

    $scope.eventEmail = eventDashboardService.getEvent().email;

    function _rosterMincount (event) {
        return (event.mincount_enter || event.mincount_accept || 7);
    }

    $scope.columnClass = function (c) {
        var stylesClasses = {
            'text-center sortable'  : true,
            'sort-asc'              : teamsTable.isSortBy(c.sortable, 'asc'),
            'sort-desc'             : teamsTable.isSortBy(c.sortable, 'desc')
        };
        if(c.selectors) {
            stylesClasses[c.selectors] = true;
        }
        return stylesClasses;
    };

    $scope.sort = function (column) {
        reloadAll = true;
        $scope.filters.page = 1;
        teamsTable.sorting(column.sortable, teamsTable.isSortBy(column.sortable, 'asc') ? 'desc'
            : teamsTable.isSortBy(column.sortable, 'desc') ? $scope.setSortDefault() : 'asc');
    };

    $scope.setSortDefault = function () {
        $scope.defaultSort = true;
        return 'text-center sortable';
    };

    var omitFilterProps = ['limit', 'page', 'revert'];

    $scope.isFiltersApplied = function () {

        var filters = $scope.filters;

        var keys = Object.keys(filters);

        for(var i = 0, f, k; i < keys.length; ++i) {
            k = keys[i];

            if(omitFilterProps.indexOf(k) === -1) {

                f = filters[k];

                if(f && f.length) {
                    return true;
                }

            }
        }

        return false;
    };

    var clearHandlers = [];

    $scope.addClearFilterHandler = function (handler) {
        clearHandlers.push(handler);
    };

    $scope.clearFilters = function () {
        clearHandlers.forEach(function (handler) {
            if(_.isFunction(handler)) {
                handler();
            }
        });

        reloadAll               = true;

        $scope.filters.search   = undefined;
        $scope.filters.entry    = undefined;
        $scope.filters.payment  = undefined;
        $scope.filters.housing  = undefined;
        $scope.filters.members  = undefined;
        $scope.filters.division = undefined;
        $scope.filters.page     = 1;

        $scope.utils.division   = null;
    };

    $scope.trust_html = function (html) {
        return $sce.trustAsHtml(html);
    };

    var isLoadingFinished = true;
    $scope.loadMore = function () {
        var isLoading   = $scope.teams_table.settings().$loading,
            maxItems    = $scope.filters.limit * $scope.filters.page;

        if (!isLoadingFinished || isLoading || $scope.data.total < maxItems || $scope.utils.allPagesEnabled) {
            console.log(
                'Load declined:', 
                !isLoadingFinished, isLoading, $scope.data.total, maxItems, $scope.data.total < maxItems
            );
            return;
        }
        isLoadingFinished = false;

        ++$scope.filters.page;

        $log.debug(
            'loadMore:', 'total', $scope.data.total, 
            'limit', $scope.filters.limit, 'page', $scope.filters.page
        );
    };

    function clearSelection () {
        $scope.data.checkboxes.qty = 0;

        var selection = $scope.data.checkboxes.items;

        Object.keys(selection).forEach(function (teamID) {
            selection[teamID] = undefined;
        });
    }

    $scope.toggleSelection = function () {
        var isSelected  = $scope.data.checkboxes.checked, 
            teams       = $scope.data.teams;

        $scope.data.checkboxes.qty = 0;
        teams.forEach(function (t) {
            $scope.data.checkboxes.items[t.roster_team_id] = isSelected;
        });

        $scope.data.checkboxes.has_selections = isSelected;
    };

    $scope.toggleTeamSelection = function () {
        var c = $scope.data.checkboxes.qty = this.countSelected();

        if(c === this.$data.length) {
            this.data.checkboxes.checked = true;
        } else {
            this.data.checkboxes.checked = false;
        }
    };

    $scope.$on('saveTeamEntryStatus', function (e, data) {
        $log.debug('saveTeamEntryStatus', data);
        for(var i = 0; i < $scope.data.teams.length; ++i) {
            if($scope.data.teams[i].roster_team_id === data.team_id) {
                $scope.data.teams[i].status_entry = data.status_entry; 
                break;
            }
        }
    });

    $scope.openModal = function (team_id, tab = null, e = null) {
        $scope.$parent.showTeamInfoModal(team_id, tab, e, $scope)
        .result.then(function (reload) {
            if(reload === true) {
                reloadData();
            }
        }, function (reload) {
            // remove `rosterTeam` from query param after modal closes
            if($scope.getTeamIdParams()){
                $scope.removeTeamIdFromParams();
            }

            if(reload === true) {
                reloadData();
            }
        });
    };

    $scope.getTeamIdParams = function () {
        const { rosterTeam } = $location.search();
        
        const rosterTeamId = Number(rosterTeam)

        return rosterTeamId || null;
    }

    $scope.removeTeamIdFromParams = function() {
        $location.search('rosterTeam', null);
    };

    $scope.handleTeamClick = function (e) {
        var target  = angular.element(e.target),
            team_id = target.closest('tr').attr('team'),
            tabNum  = target.attr('tabid');

        if(!team_id) return;

        return this.openModal(team_id, tabNum, e);
    }

    $scope.filterStatus = function (status, selection) {
        console.log('filter status', status, selection);
        reloadAll = true;
        $scope.filters[status] = selection;
        $scope.filters.page    = 1;

        $scope.data.checkboxes.checked = false;
    };

    $scope.filterEntry      = $scope.filterStatus.bind(null, 'entry');
    $scope.filterPayment    = $scope.filterStatus.bind(null, 'payment');
    $scope.filterHousing    = $scope.filterStatus.bind(null, 'housing');
    $scope.filterMembers    = $scope.filterStatus.bind(null, 'members'); 
    $scope.filterDivision   = function (selection) {
        if(selection.length === 1) {
            $scope.utils.division = findPickedDivision(selection[0]);
        } else {
            $scope.utils.division = null;
        }
        $scope.filterStatus('division', selection);
    };

    $scope.filterSearch     = function () {
        if($scope.teams_table.settings().$loading) return;

        reloadAll = true;
        $scope.filters.search   = $scope.utils.search;
        $scope.filters.page     = 1;

        $scope.data.checkboxes.checked = false;
    };

    function findPickedDivision (id) {
        var divisions = $scope.data.divisions;

        for(var i = 0, d; i < divisions.length; ++i) {

            d = divisions[i];

            if(d.id == id) {
                return d;
            }
        }

        return null;
    }

    $scope.countSelected = function () {
        var _count = 0;
        angular.forEach($scope.data.checkboxes.items, function (value) {
            if(value) _count++;
        });
        $scope.data.checkboxes.has_selections = (_count > 0);
        return _count;
    };

    function pickLoadedTeams () {

        var teams = $scope.data.teams;

        teams.forEach(function (team) {
            $scope.data.checkboxes.items[team.roster_team_id] = true;
        });
    }

    $scope.getFiltersValues = function () {
        var params = _.omit($scope.filters, 'limit', 'page');

        if($scope.data.checkboxes.has_selections && !$scope.data.checkboxes.checked) {
            params.teams = getPickedTeams();
        } 
 
        return params;
    };

    $scope.getTeams = function() {
        const params    = {};
        const filters   = _.omit($scope.filters, 'limit', 'page');

        if ($scope.data.checkboxes.has_selections) {
            params.teams = getPickedTeams();
        } else if (!_.isEmpty(filters)) {
            params.teams = $scope.data.teams.map(({ roster_team_id }) => roster_team_id);
        } else {
            params.teams = null;
        }

        return params;
    };

    $scope.teamRowClass = function (id) {
        return {
            'bg-info': $scope.data.checkboxes.items[id]
        }
    }

    function openModalWithTeamIdParam () {
        const rosterTeamId = $scope.getTeamIdParams();

        if(rosterTeamId) {
            $scope.openModal(rosterTeamId, 5);
        }
    }

    function loadDivisions () {
         divisionsService.getDivisions($stateParams.event, { sort: true })
        .then(function (divisions) {
            $scope.data.divisions = _.map(divisions, function (d) {

                d.class         = 'fa ' + getGenderIcon(d.gender);
                d.id            = d.division_id;
                d.item_class    = d.gender;
                d.short         = d.short_name;

                return d;
            });

            if($scope.filters.division && $scope.filters.division[0]) {
                $scope.utils.division = findPickedDivision($scope.filters.division[0]);            
            }
        });
    }

    var genderIcons = { 'male': 'fa-male', 'female': 'fa-female', coed: 'fa-male fa-female' };
    function getGenderIcon (gender) {
        return genderIcons[gender];
    }

    function getPickedTeams () {
        var items = $scope.data.checkboxes.items;

        return _.filter(Object.keys(items), function (teamID) {
            return !!items[teamID];
        });
    }

    function updateListData(teams) {
        const filters = Object.assign(
            {teams},
            _.omit(
                $scope.teams_table.filter(),
                ['order','revert','page','limit']
            )
        );
        AllTeamsService.loadTeams($stateParams.event, filters).then(resp => {
            let newTeamsData = resp.data.teams;

            updateTeamsInList(newTeamsData);
        })
    }

    function updateTeamsInList(newData) {
        newData.forEach(newTeamData => {

            $scope.data.teams.forEach((oldTeamData, id) => {
                if(newTeamData.roster_team_id === oldTeamData.roster_team_id) {
                    $scope.data.teams[id] = newTeamData;
                }
            })

        });
    }

    $scope.formatDateForTable = function (date, columnName, distance) {
        if(date === 'Local') {
            return date;
        }

        if(date) {
            return moment(date, 'MM/DD/YYYY, h:mm a').format('MM/DD');
        } else {
            if (columnName === 'housing_date' && distance !== null) {
                return '--';
            }
        }
    };

    $scope.formatDateForTooltip = function (team, column) {
        if(column.name === 'created_date') {
            if(team['purchase_type'] === PAYMENT_TYPE.CHECK) {
                return undefined;
            } else if(team['purchase_type'] === PAYMENT_TYPE.ACH) {
                return formatDateForACHPaymentTypeTooltip(team);
            }
        }

        const date              = team[column.name];
        const formattedDate     = moment(date, 'MM/DD/YYYY, h:mm a').format('MM/DD h:mm a');

        const { distance_to_event: distance } = team;

        if (date && (column.name !== 'housing_date')) {
            return formattedDate;
        }

        const distanceMessage = distance !== null ? `Distance to event ${distance} miles` : '';
        const dateMessage = `Housing date ${formattedDate}`;

        if(date && date !== 'Local') {
            return `${distanceMessage} ${dateMessage}`;
        } else {
            return distanceMessage;
        }
    };

    function formatDateForACHPaymentTypeTooltip(team) {
        if(team['status'] === PAYMENT_STATUS.PAID) {
            return $sce.trustAsHtml(`
                Created: ${moment(team['created_date'], DB_DATETIME_FORMAT).format(MONTH_DAY_DATETIME_FORMAT)}<br>
                Paid: ${moment(team['paid_date'], DB_DATETIME_FORMAT).format(MONTH_DAY_DATETIME_FORMAT)}
            `);
        } else {
            return $sce.trustAsHtml(`
                Created: ${moment(team['created_date'], DB_DATETIME_FORMAT).format(MONTH_DAY_DATETIME_FORMAT)}
            `);

        }
    }

    $rootScope.$on('updateTeamsData', function (event, teams) {
        updateListData(teams);
    });

    $scope.showNotCompletedProfileIcon = (c, t) => {
        return c.name === 'club_name' && !t.profile_completed;
    };

    openModalWithTeamIdParam();
    loadDivisions();
}
