angular.module('SportWrench').service('assignTeamService', assignTeamService);

assignTeamService.$inject = ['$http'];

function assignTeamService ($http) {
    this.$http = $http;
}

assignTeamService.prototype.getTeamsList = function (eventId, params) {
    return this.$http.get('/api/v2/event/' + eventId + '/club/teams/available', {
        params: params
    })
}

assignTeamService.prototype.assignTeam = function (eventId, data) {
    return this.$http.post('/api/v2/event/' + eventId + '/club/teams/assign', data)
}
