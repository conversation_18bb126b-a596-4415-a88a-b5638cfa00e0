<div class="row row-space allteams filters-panel">
    <div class="col-lg-2">
        <sw-searchbox
            css="search_teams white-ro"
            reload="filterSearch()"
            input-model="utils.search"
            placeholder="Search ..."
            is-readonly="teams_table.settings().$loading"
            reg-on-clear="addClearFilterHandler(handler)"
            reload-time="750"
        >   
            <info>
                <small>
                    <a 
                        href="" 
                        class="q-ty" 
                        ng-if="data.total > 0 && !utils.allPagesEnabled" 
                        ng-click="loadAllPagesTeams()"
                    >Total: {{data.total}}</a>  
                    <span ng-if="data.total > 0 && utils.allPagesEnabled">Total: {{data.total}}</span>          
                </small>
            </info>
        </sw-searchbox>
    </div>
    <div class="col-lg-8">
        <div class="form-inline">
            <team-status-picker
                items="data.divisions"
                on-select="filterDivision(selection)"
                lb="division"
                picker-name="All Divisions"
                icon="all-teams-icon glyphicon glyphicon-th-large"
                size="large"
                group-limit="2"
                init="filters.division"
                reg-on-clear="addClearFilterHandler(handler)"
            >
                <div ng-if="utils.division">
                    <small>
                        <b>Max:</b> {{utils.division.max_teams}} <b>Accepted:</b> {{utils.division.accepted}} <b>Waitlisted:</b> {{utils.division.wait}}
                    </small>
                </div>
            </team-status-picker>
            <team-status-picker
                items="data.entry_statuses"
                on-select="filterEntry(selection)"
                lb="entry"
                picker-name="Entry"
                icon="fa fa-list-alt big-icon"
                reg-on-clear="addClearFilterHandler(handler)"
            >
            </team-status-picker>
            <team-status-picker
                items="data.payment_statuses"
                on-select="filterPayment(selection)"
                lb="payment"
                picker-name="Payment"
                icon="fa fa-money big-icon"
                reg-on-clear="addClearFilterHandler(handler)"
            >
            </team-status-picker>
            <team-status-picker
                items="data.housing_statuses"
                on-select="filterHousing(selection)"
                lb="housing"
                icon="all-teams-icon glyphicon glyphicon-home"
                picker-name="Housing"
                ng-if="($parent.event.registration_method !== 'doubles') && event.has_status_housing"
                size="large"
                reg-on-clear="addClearFilterHandler(handler)"
            >        
            </team-status-picker>        
            <team-status-picker
                ng-if="event.has_rosters"
                size="middleLarge"
                items="data.members"
                on-select="filterMembers(selection)"
                lb="members"
                icon="fa fa-users big-icon"
                picker-name="Members"
                reg-on-clear="addClearFilterHandler(handler)"
            >
            </team-status-picker>
        </div>
    </div>
    <div class="col-lg-2">
        <div class="col-lg-5">
            <button class="ctrl-btn btn btn-default text-danger" ng-if="isFiltersApplied()" uib-tooltip="Reset All Filters" ng-click="clearFilters()">
                <i class="fa fa-times fa-lg" title="Clear All filters"></i>
                <span>Reset</span>
            </button>
        </div>
        <div class="col-lg-5">
            <teams-actions
                event="eventID"
                reload="reloadTeams()"
                event-has-rosters="hasRosters"
                event-has-status-housing="hasStatusHousing"
                is-doubles="isDoubles"
                can-add-teams-manually="canAddTeamsManually"
                has-clubs="hasClubs"
                get-params="getFiltersValues()"
                get-teams="getTeams()"
                email="eventEmail"
                picked="{{data.checkboxes.qty || data.total}}"
                teams-qty="data.total"
                has-manual-club-names="hasManualClubNames"
                has-coupons="hasCoupons"
                divisions="data.divisions">
            </teams-actions>
        </div>
    </div>
</div>

<div loading-container="teams_table.settings().$loading">
    <table 
        class="table sw-adaptive-grid table-condensed event-all-teams-table ng-table" 
        ng-table="teams_table" 
        infinite-scroll="loadMore()" 
        infinite-scroll-distance="2" 
        infinite-scroll-disabled="teams_table.settings().$loading" 
        sticky-header
        >
        <thead>
            <tr>
                <th width="65" style="text-align: left">
                    <ng-include src="'ng-table/headers/checkbox.html'"></ng-include>
                </th>
                <th>
                    <ng-include src="'ng-table/headers/statuses.html'"></ng-include>
                </th>
                <th
                    ng-repeat="c in ::dynamic_columns" 
                    ng-if="::c.visible" 
                    ng-class="columnClass(c)"
                    ng-click="sort(c)">
                    <div class="sort-indicator">{{c.title}}</div>
                </th>                
            </tr>
        </thead>
        <tbody ng-click="handleTeamClick($event)">
            <tr ng-repeat="t in $data track by t.roster_team_id" team="{{t.roster_team_id}}" ng-class="teamRowClass(t.roster_team_id)">
                <td width="65" class="text-left" header="'ng-table/headers/checkbox.html'" ng-click="$event.stopPropagation()">
                    <input 
                        type="checkbox"
                        ng-model="data.checkboxes.items[t.roster_team_id]"
                        ng-change="toggleTeamSelection()"
                        >
                </td>
                <td class="pointer" ng-bind-html="trust_html(t.icons)"></td>
                <td
                    ng-repeat="c in ::dynamic_columns" 
                    ng-if="c.visible" 
                    sortable="c.sortable"              
                    class="pointer {{c.selectors}}"
                    >
                    <i ng-if="c.name === 'team_name' && t.locked" class="fa fa-lock" aria-hidden="true"></i>
                    <i
                        ng-if="showNotCompletedProfileIcon(c,t)"
                        class="fa fa-exclamation-triangle t-error"
                        aria-hidden="true"
                        uib-tooltip="This club had not updated their profile"
                    >
                    </i>
                    <i
                        ng-if="c.name === 'housing_date' && !t.is_local && t.distance_to_event === null"
                        class="fa fa-exclamation-triangle t-error"
                        uib-tooltip="This club doesn't have distance to the event"
                    >
                    </i>
                    <span ng-if="c.is_date"
                          tooltip-class="text-nowrap"
                          uib-tooltip-html="'{{formatDateForTooltip(t, c)}}'">
                        {{formatDateForTable(t[c.name], c.name, t.distance_to_event)}}
                    </span>
                    <span ng-if="!c.is_date">
                        {{t[c.name]}}
                    </span>
                </td>
            </tr>
            <tr class="bg-info text-center" ng-if="teams_table.settings().$loading">
                <td colspan="{{3 + dynamic_columns.length}}">Loading ...</td>
            </tr>
        </tbody>   
    </table>
</div>

<script type="text/ng-template" id="ng-table/headers/checkbox.html">
    <input type="checkbox" ng-model="data.checkboxes.checked" ng-change="toggleSelection()" class="pull-left"/>
    <span class="badge badge-dark"><small>{{data.checkboxes.checked?data.total:data.checkboxes.qty}}</small></span>
</script>

<script type="text/ng-template" id="ng-table/headers/statuses.html">
    <span 
        class="fa fa-list-alt big-icon" 
        title="Entry status" 
        >
    </span>
    <span 
        class="fa fa-money big-icon" 
        title="Paid status" 
        >
    </span>
    <span 
        class="all-teams-icon glyphicon glyphicon-home" 
        title="Housing status" 
        ng-if="event.has_status_housing" 
        >
    </span>
    <span
        ng-if="event.has_rosters"
        class="fa fa-users big-icon"
        title="Roster status" 
        >
    </span>
</script>
