

class Controller {

    constructor (ticketsAdditionalService) {
        this.generateOptionLabel = ticketsAdditionalService.generateFieldName.bind(ticketsAdditionalService);
    }

    addOption () {
        if(this.readOnly) {
            return;
        }

        if(!this.newOption) {
            return;
        }

        const id = this.generateOptionLabel(this.newOption);
        const lastID =  this.options.length ? this.options.length : 0;

        if(!this.options[lastID]) {
            this.options[lastID] = {
                id,
                label: this.newOption
            };
        }

        this.newOption = null;
    }

    saveChanges (cb) {
        cb();
    }

    save () {
        if(this.readOnly) {
            return;
        }

        this.onSave({ options: this.options });
    }

    removeOption (id) {
        if(this.readOnly) {
            return;
        }

        this.options.splice(id, 1);
    }

}

Controller.$inject = ['ticketsAdditionalService'];

angular.module('SportWrench').component('customFormSelectOptions', {
    templateUrl: 'events/dashboard/custom-forms-builder/custom-form-editor/custom-form-fields-editor/custom-form-field-editor/custom-form-select-options/custom-form-select-options.template.html',
    bindings: {
        options: '<',
        onSave: '&',
        close: '&',
        readOnly: '<'
    },
    controller: Controller
});
