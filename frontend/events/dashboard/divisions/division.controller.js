angular.module('SportWrench').controller('Event.DivisionContoller', DivisionContoller);

DivisionContoller.$inject = [
    '$scope', 'mode', 'divisionsService', 'division', '$uibModalInstance', '$stateParams', '$filter', 'toastr',
    'eventDashboardService', 'UtilsService', 'QUALIFIER_EVENT_TYPE', 'ESW_URL', 'SANCTIONING_BODY', 'INTEGER_PATTERN'
];

function DivisionContoller (
    $scope, mode, divisionsService, division, $uibModalInstance, $stateParams, $filter, toastr,
    eventDashboardService, UtilsService, QUALIFIER_EVENT_TYPE, ESW_URL, SANCTIONING_BODY, INTEGER_PATTERN
) {
    var dateFormat = 'MM/dd/yyyy HH:mm',
        dateFilter = $filter('date');

    const OMIT_FIELDS = [
        'has_flowchart_image',
        'esw_id',
        'division_id',
    ];

    const ALLOWED_TYPES = [
        'image/png',
        'image/jpeg',
    ];

    const ALLOWED_SIZE = '500';

    $scope.division     = (mode === 'edit')?division:{ locked: false, published: true };
    $scope.event        = eventDashboardService.getEvent();

    $scope.fileInputId      = 'flowchartImage';
    $scope.fileToUpload     = '';
    $scope.errorMessage     = '';
    $scope.showPreviewLink  = $scope.division.has_flowchart_image && $scope.division.published;
    $scope.previewUrl       = `${ESW_URL}/#/events/${$scope.division.esw_id}/divisions/${$scope.division.division_id}/flowchart`;
    $scope.isEventHasUSAVSanctioning = $scope.event.sport_sanctioning_id === SANCTIONING_BODY.USAV;
    $scope.INTEGER_PATTERN = INTEGER_PATTERN;

    $scope.onChange = () => {
        if (!$scope.fileToUpload) {
            return;
        }

        try {
            validateFile();

            $scope.errorMessage = '';
        } catch (e) {
            clearInput();

            $scope.errorMessage = e;
        }
    };
    
    $scope.dl_picker    = {
        opened: false
    }
    $scope.dr_picker    = {
        opened: false
    }
    $scope.utils = {
        formSubmitted: false,
        ages: divisionsService.getAges()
    };

    var numberOfEventGenders = ($scope.event.has_coed_teams
                                + $scope.event.has_female_teams
                                + $scope.event.has_male_teams);

    if(mode === 'create') _watchName();
    
    var _createDivision = function (d) {
        return divisionsService.createDivision($stateParams.event, d);
    };

    var _updateDivision = function (d) {
        const formData = new FormData();
        const division = _.omit(d, ...OMIT_FIELDS);

        formData.append('division', angular.toJson(division));
        formData.append('file', $scope.fileToUpload);

        return divisionsService.updateDivision($stateParams.event, $stateParams.division, formData);
    };

    $scope.save = function () {
        $scope.utils.formSubmitted = true;
        if($scope.divisionForm.$invalid) {
            toastr.warning('Invalid Form Values')
            return;
        }
        let d = angular.copy($scope.division);
        
        d.max_age           = parseInt(d.max_age, 10);
        d.max_teams         = parseInt(d.max_teams, 10);
        d.max_auto_enter    = parseInt(d.max_auto_enter, 10);
        d.max_waiting       = parseInt(d.max_waiting, 10);
        d.has_usav_sanctioning = $scope.isEventHasUSAVSanctioning;

        if(d.roster_deadline) 
            d.roster_deadline = dateFilter(d.roster_deadline, dateFormat);
        if(d.date_reg_close) 
            d.date_reg_close = dateFilter(d.date_reg_close, dateFormat);

        if(d.reg_fee > 0) {
            d.reg_fee = parseFloat(d.reg_fee, 10);
        } else {
            d.reg_fee = null;
        }

        if(d.credit_surcharge) {
            d.credit_surcharge = Number(d.credit_surcharge);
        } else {
            d.credit_surcharge = null;
        }

        d.level = d.level || null;

        (
            (mode === 'create')?_createDivision(d):_updateDivision(d)
        ).then(function () {
            $uibModalInstance.close();
        }).catch(function(error) {
            // check if short name is unique
            //toastr.warning('Short Name must be unique')
            //return;
        })
    }

    $scope.modalTitle = function () {
        return ((mode === 'edit')?'Update':'Create') + ' Division';
    }

    $scope.isQualifyingEvent = function () {
        return $scope.event && $scope.event.event_type === QUALIFIER_EVENT_TYPE;
   }

    function _watchName () {
        $scope.$watch('[division.max_age, division.level, division.gender]', function(n) {
            if (n[0] && n[1]) {
                if(n[0] === '0') {
                    $scope.division.name = '';
                    $scope.division.short_name = '';
                } else {
                    $scope.division.name = (n[0] + ' ' + n[1]);
                    $scope.division.short_name = divisionsService.generateShortName(n[0], n[1], n[2], 
                                                                                    numberOfEventGenders, true);                        
                }
            }
        }, true);
    }

    const clearInput = () => {
        angular.element(`#${$scope.fileInputId}`).val(null);
        $scope.fileToUpload = null;
    };

    const validateFile = () => {
            const isAllowType = ALLOWED_TYPES.some(type => $scope.fileToUpload.type === type);
            const fileSizeInKB = UtilsService.getNextBinaryPrefixValue($scope.fileToUpload.size);

            if (!isAllowType) {
                throw 'Allowable PNG or JPEG types';
            }

            if (fileSizeInKB > ALLOWED_SIZE) {
                throw 'Maximum size for image 500KB';
            }
    }
}
