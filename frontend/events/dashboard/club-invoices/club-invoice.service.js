class ClubInvoiceService {
    constructor ($http, $uibModal) {
        this.$http = $http;
        this.$uibModal = $uibModal;
    }

    getClubsList (event) {
        return this.$http.get(`/api/event/${event}/club-invoice/clubs`)
            .then(response => response.data && response.data.clubs);
    }

    getClubInvoicesList (event, params) {
        return this.$http.get(`/api/event/${event}/club-invoice`, { params: params })
            .then(response => response.data && response.data.clubInvoices);
    }

    getClubInvoicesDetails (event, invoice) {
        return this.$http.get(`/api/event/${event}/club-invoice/${invoice}`)
            .then(response => response.data && response.data.invoice);
    }

    createClubInvoice (event, data) {
        return this.$http.post(`/api/event/${event}/club-invoice`, data)
            .then(response => response.data && response.data.invoice);
    }

    makeFullRefund (event, invoice) {
        return this.$http.post(`/api/event/${event}/club-invoice/${invoice}/refund/full`);
    }

    cancelClubInvoice (event, invoice) {
        return this.$http.post(`/api/event/${event}/club-invoice/${invoice}/cancel`);
    }

    openCreationModal () {
        return this.$uibModal.open({
            backdrop: 'static',
            size: 'md',
            template: `
                <div class="modal-body">
                    <club-invoice-editor on-close="$close()"/>
                </div>`,
        }).result;
    }

    openDetailsModal (clubInvoice) {
        const {purchase_id: purchaseId} = clubInvoice;

        return this.$uibModal.open({
            backdrop: 'static',
            size: 'md',
            template: `
                <div class="modal-body">
                    <club-invoice-details purchase-id="purchaseId" on-close="$close()" />
                </div>
                <div class="modal-footer">
                    <button class="btn btn-default pull-right" ng-click="$close()">Close</button>
                    <button class="btn btn-danger pull-right" ng-if="showDeleteButton()" ng-click="deleteInvoice()">Delete invoice</button>
                </div>
            `,
            controller: ['$scope', '$rootScope', '$stateParams', 'PAYMENT_TYPE', 'PAYMENT_STATUS', 'clubInvoiceService', 'toastr',
            async function($scope, $rootScope, $stateParams, PAYMENT_TYPE, PAYMENT_STATUS, clubInvoiceService, toastr) {
                $scope.eventId = $stateParams.event;
                $scope.purchaseId = purchaseId;

                $scope.showDeleteButton = function () {
                    return clubInvoice.type === PAYMENT_TYPE.PENDING_PAYMENT && clubInvoice.status === PAYMENT_STATUS.PENDING;
                }

                $scope.deleteInvoice = async function () {
                    await clubInvoiceService.cancelClubInvoice($scope.eventId, $scope.purchaseId);

                    toastr.success('Club Invoice Deleted');
                    $rootScope.$broadcast('club.invoice.updated');
                    this.$close();
                }
            }]
        }).result;
    }
}

ClubInvoiceService.$inject = ['$http', '$uibModal'];

angular.module('SportWrench').service('clubInvoiceService', ClubInvoiceService);
