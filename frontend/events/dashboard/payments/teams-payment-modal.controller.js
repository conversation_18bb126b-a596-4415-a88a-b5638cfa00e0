angular.module('SportWrench').controller('EventInfoModalController', EventInfoModalController);

EventInfoModalController.$inject = ['$scope', '$uibModalInstance', '$stateParams', 'purchaseService', 'eventPaymentsService'];

function EventInfoModalController ($scope, $uibModalInstance, $stateParams, purchaseService, eventPaymentsService) {

    $scope.cancel = function() {
        $uibModalInstance.dismiss($scope.dataHasChanged);
    };

    $scope.utils            = { loading: true };
    $scope.payment          = $scope.$parent.payment;
    $scope.teams            = $scope.payment.teams;
    $scope.teamPayments     = {};

    $scope.togglePaymentMenu = function () {
        $scope.openPaymentMenu = !$scope.openPaymentMenu;
    };

    $scope.showPayments = function (team) {

        if (!$scope.paymentWindow) {
            _getPayments(team.roster_team_id);
            $scope.paymentWindow = true;
            $scope.selectedTeam = team;
        } else {
            $scope.paymentWindow = false;
            $scope.teamPayments = {};
            $scope.utils.loading = true;
            $scope.selectedTeam = {};
        }

    };

    if($scope.openPayment) {
        $scope.showPayments($scope.teams[0]);
    }

    $scope.cancelPurchase = function (id, items) {
        return purchaseService.cancel($stateParams.event, id, items).then(function (res) {
            var isFullCancellation = res.data && res.data.is_full;

            if (isFullCancellation) {
                _getPayments($scope.selectedTeam.roster_team_id);
            }

        })
    };

    function _getPayments (team) {
        var _data = {team: team};

        eventPaymentsService.eventPayments($stateParams.event, _data)
            .then(function (resp) {
                $scope.teamPayments = resp.data.payments;
                $scope.utils.loading = false;
            });

    };

}
