<div class="panel panel-default">
    <div class="panel-heading">
        <h3 class="panel-title">Statistics for {{::$ctrl.title}} (usd):</h3>
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-xs-7">{{::$ctrl.paidAmountLabel()}} ({{$ctrl.stats.paid_items_qty}})</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.paid_items_amount)"></div>
        </div>
        <div class="row" ng-if="!$ctrl.hideCardStats()">
            <div class="col-xs-7">Credit ({{$ctrl.stats.paid_items_cards_qty}})</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.paid_items_cards_amount)"></div>
        </div>
        <div class="row" ng-if="!$ctrl.hideAchStats() && !$ctrl.isTickets()">
            <div class="col-xs-7">ACH ({{$ctrl.stats.paid_items_ach_qty}})</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.paid_items_ach_amount)"></div>
        </div>
        <div class="row" ng-if="!$ctrl.isTickets()">
            <div class="col-xs-7">Check ({{$ctrl.stats.paid_items_checks_qty}})</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.paid_items_checks_amount)"></div>
        </div>

        <div class="row" ng-if="$ctrl.isTickets()">
            <div class="col-xs-7">Cash ({{$ctrl.stats.paid_items_cash_qty}})</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.paid_items_cash_amount)"></div>
        </div>


        <div class="row" ng-if="!$ctrl.hideCardStats()">
            <div class="col-xs-7">Credit Card Merchant Fee</div>
            <div class="col-xs-2 text-right"
                 ng-bind="$ctrl.getAmount('', $ctrl.stats.stripe_fee_card || $ctrl.stats.payment_method_fee)"></div>
        </div>

        <div class="row" ng-if="!$ctrl.hideCardStats() && !$ctrl.isTickets()">
            <div class="col-xs-7">ACH Merchant Fee</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.stripe_fee_ach)"></div>
        </div>

        <div class="row">
            <div class="col-xs-7">{{::$ctrl.swFeeLabel()}} ({{$ctrl.stats.sw_fee_items_qty}}*{{$ctrl.swItemFee | currency}})</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', $ctrl.stats.sw_fee)"></div>
        </div>
        <div class="row">
            <div class="col-xs-7">{{::$ctrl.netProfitLabel()}} (unless disputes)</div>
            <div class="col-xs-2 text-right font-bold" ng-bind="$ctrl.getAmount('', $ctrl.stats.net_profit)"></div>
        </div>
        <hr class="general-statictics_separate-line">
        <div ng-if="!$ctrl.hideCollectedEscrow()" class="row">
            <div class="col-xs-7">{{::$ctrl.collectedEscrowLabel()}}</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.showEscrowCollected()"></div>
        </div>
        <div ng-if="$ctrl.showDisputeInfoLabel()" class="row">
            <div class="col-xs-12 text-grey">
                <span ng-class="{ 'vis-hidden': ! $ctrl.isTeams() }">Event owner is responsible for any disputes lost not covered by escrow</span>
            </div>
        </div>


        <div class="row" ng-if="$ctrl.stats.uncollected_sw_fee > 0">
            <div class="col-xs-7">Uncollected SW {{$ctrl.uncollectedSWFeeLabel()}} Fee</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.getAmount('', -$ctrl.stats.uncollected_sw_fee)"></div>
        </div>

        <virtual-transfers-list 
            ng-if="$ctrl.stats.virtual_transfers_list.length > 0" 
            transfers="$ctrl.stats.virtual_transfers_list"
        ></virtual-transfers-list>


        <div class="row" ng-if="$ctrl.stats.lost_disputes.qty > 0">
            <div class="col-xs-7 font-bold">Disputes Penalty
                <span ng-if="$ctrl.stats.lost_disputes.qty_before_change > 0">
                    ({{$ctrl.stats.lost_disputes.qty_before_change}} * {{$ctrl.getAmount('$', $ctrl.stats.lost_disputes.fee_before_change)}})
                </span>
                <span ng-if="$ctrl.stats.lost_disputes.qty_after_change > 0">
                    ({{$ctrl.stats.lost_disputes.qty_after_change}} * {{$ctrl.getAmount('$', $ctrl.stats.lost_disputes.fee_after_change)}})
             </span>
            </div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.showLostDisputesPenalty()"></div>
        </div>

        <div class="row" ng-if="$ctrl.stats.lost_ach_payments.qty > 0">
            <div class="col-xs-7 font-bold">Failed ACH Payments Penalty <span>({{$ctrl.stats.lost_ach_payments.qty}} * {{$ctrl.getAmount('$', $ctrl.stats.lost_ach_payments.fee)}})</span></div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.showFailedACHPenalty()"></div>
        </div>

        <div class="row" ng-if="$ctrl.stats.paid_escrow.paid > 0">
            <div class="col-xs-7 font-bold">Paid SW Fee by card</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.showPaidByCardEscrow()"></div>
        </div>

        <div class="row" ng-if="$ctrl.stats.paid_escrow.pending > 0">
            <div class="col-xs-7 font-bold">Paid SW Fee by card (pending)</div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.showPendingByCardEscrow()"></div>
        </div>

        <div class="row" ng-if="$ctrl.stats.lost_disputes.qty > 0">
            <div class="col-xs-7 font-bold">Lost Disputes Stripe Fee <span>({{$ctrl.stats.lost_disputes.qty}})</span></div>
            <div class="col-xs-2 text-right" ng-bind="$ctrl.showLostDisputesStripeFee()"></div>
        </div>

        <virtual-transfers-list 
            ng-if="$ctrl.stats.escrow_transfers_list.length > 0" 
            transfers="$ctrl.stats.escrow_transfers_list"
        ></virtual-transfers-list>

        <div class="row">
            <div class="col-xs-7 font-bold text-danger">
                <span ng-class="{ 'vis-hidden': $ctrl.hasNoEscrow() }">{{$ctrl.owedEscrowLabel()}}</span>
            </div>
            <div class="col-xs-2 text-right">
                <span 
                    ng-class="{ 'vis-hidden': $ctrl.hasNoEscrow() }" 
                    ng-bind="$ctrl.getAmount('', $ctrl.abs($ctrl.stats.escrow_still_owned))">
                </span>
            </div>
        </div>

        <div class="row row-space" ng-if="$ctrl.allowToPayUncollectedFee()">
            <div class="col-xs-4">
                <button class="btn btn-default" ng-click="$ctrl.openPayUncollectedFeeModal()">
                    Pay Uncollected Fee
                </button>
            </div>
        </div>
    </div>
</div>
