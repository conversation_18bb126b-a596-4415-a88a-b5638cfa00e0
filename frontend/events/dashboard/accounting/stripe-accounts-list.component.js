angular.module('SportWrench').component('stripeAccountList', {
    templateUrl: 'events/dashboard/accounting/stripe-accounts-list.html',
    bindings: {
        accounts: '<',
        onConnect: '&'
    },
    controller: ['StripeService', 'toastr', function (StripeService, toastr) {

        this.loading = {
            error: false,
            errMsg: ''
		};
		
		this.utils = {
			showRegistrationInfo  		: false,
			registringAccountIndex		: -1,
			platformClientIdRegEx		: StripeService.platformClientIdPattern(),
			savingInProcess				: false
		}

        this.connect = function (account) {
            this.onConnect({
                account: account
            });
        };

		this.toggleHidden = function (account) {
            const updatedHidden = !account.hidden
            const visibility = updatedHidden ? "hidden" : "visible";

            StripeService.toggleStripeAccountVisibility(
                account.account_id,
                visibility
            )
                .then(() => {
                    this.loading.error = false;
                    account.hidden = updatedHidden;
                })
                .catch((err) => {
                    this.loading.error = true;
                    this.loading.errMsg =
                        err.data && err.data.validation
                            ? err.data.validation
                            : "Internal Server Error.";
                });
        };

        this.getPlatformRegistrationURL = function () {
            return StripeService.getPlatformRegistrationURL();
        };

        this.canRegisterPlatform = function (acc) {
            return acc.account_type == StripeService.platformStripeAccountType();
		};
		
		this.showClienIdRegistrationForm = function(index) {
			if(this.utils.registringAccountIndex == index) {
				//togle if we click the same button
				this.utils.showRegistrationInfo = !this.utils.showRegistrationInfo;
			} else {
				// otherwise show registration info
				this.utils.showRegistrationInfo = true;
			}
			
			this.utils.registringAccountIndex = index;
		};

		this.savePlatformClientId = function (acc) {
			const clientID = acc.platform_client_id || 0;

			if (!clientID || !this.utils.platformClientIdRegEx.test(clientID)) {
				this.clientIDForm.$setValidity('platformClientIdValid', false);
				return;
			}

			// avoid double clicking Save Platform Client ID
			this.utils.savingInProcess = true;
			StripeService.savePlatformClientId(acc.id, clientID)
				.then(() => {
					this.utils.showRegistrationInfo = false;
					this.utils.registringAccountIndex = -1;
					this.utils.savingInProcess = false;
					this.loading.error = false;
					acc.is_platform = true;
					toastr.success(`Platform Client ID for Account "${acc.title}" Saved`);
				})
				.catch((err) => {
					this.utils.savingInProcess = false;
					this.loading.error = true;
					this.loading.errMsg = (err.data && err.data.validation) ?
						err.data.validation :
						'Internal Server Error.'
				});

		};
    }]
});
