<div class="row-space"></div>
<spinner active="!$ctrl.loading.data_loaded"></spinner>
<div class="panel panel-default" ng-if="$ctrl.loading.data_loaded">
    <div class="panel-heading">
        <h3 class="panel-title">{{ $ctrl.panelTitle }}</h3>
    </div>
    <div class="panel-body">
        <uib-alert type="danger" ng-if="$ctrl.loading.error">{{ $ctrl.loading.errMsg }}</uib-alert>
        <table class="table table-condensed" ng-if="!$ctrl.loading.error">
            <tbody>
                <tr>
                    <td><label>{{ $ctrl.dueToEventLabel }}</label></td>
                    <td class="text-right">
                        <span uib-tooltip="{{ $ctrl.doToEventAmountToolTip }}">
                            {{ $ctrl.details.dueToEvent | currency}}
                        </span>
                    </td>
                </tr>
                <tr ng-if="$ctrl.paymentFor ==='tickets'">
                    <td><label>ESCROW ACCT BEING HELD</label></td>
                    <td class="text-right">
                            {{ $ctrl.details.escrowAccBeingHeld | currency}}
                    </td>
                </tr>
                <tr ng-if="$ctrl.paymentFor ==='tickets'">
                    <td class="text-right"><label>TOTAL AVAILABLE FOR TICKETS</label></td>
                    <td class="text-right">
                            {{ $ctrl.details.totalAvailableForTickets | currency}}
                     </td>
                </tr>
                <tr ng-if="$ctrl.paymentFor ==='tickets'"><td colspan="2"></td></tr>
                <tr>
                    <td><label>MANUAL PAYOUTS</label></td>
                    <td class="text-right">
                        <span uib-tooltip="Transfers done through SportWrench to EO's bank acct">
                            {{ $ctrl.details.manualPayouts | currency}}
                        </span>
                    </td>
                </tr>
                <tr><td colspan="2"></td></tr>
                <tr>
                    <td><label>STILL AVAILABLE TO PAYOUT</label> </td>
                    <td class="text-right">
                            {{ $ctrl.details.stillAvailableToPauout | currency}}
                    </td>
                </tr>
                <tr><td colspan="2"></td></tr>
                <tr>
                    <td td colspan="2"><label>ADJUSTMENTS BY STRIPE</label></td>
                </tr>
                <tr>
                    <td class="text-right"><label>Lost Disputes</label></td>
                    <td class="text-right">
                        <span uib-tooltip="Stipe deductions  from EO's bank acct">
                            {{ $ctrl.details.lostDisputes | currency}}      
                        </span>
                    </td>
                </tr>
                <tr>
                    <td class="text-right"><label>Refund Adjustments</label> </td>
                    <td class="text-right">
                        <span uib-tooltip="Stipe refunds to EO's bank acct">
                            {{ $ctrl.details.refundAdjustments | currency}}        
                        </span>
                    </td>
                </tr>
                <tr><td colspan="2"></td></tr>
                <tr>
                    <td><label>STRIPE ACCT TOTAL FOR THIS EVENT</label> </td>
                    <td class="text-right">
                        <span uib-tooltip="Final Amount from Stripe to EO's bank acct">
                            {{ $ctrl.details.stripeAcctTotalForThisEvent | currency}}         
                        </span>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
