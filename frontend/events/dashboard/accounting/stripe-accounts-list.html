<uib-alert type="danger" ng-if="$ctrl.loading.error">{{ $ctrl.loading.errMsg }}</uib-alert>
<!-- List of EO Stripe Accounts -->
<ul class="list-group stripe-accounts-list">
    <li class="list-group-item" ng-repeat="acc in $ctrl.accounts">
    	<div class="row">
    		<div class="col-xs-8 acc-title">
				<span class="label label-warning" ng-if="acc.is_test">test</span>
    			<span class="text-bold" ng-bind="acc.title"></span>
    			<span ng-bind="acc.email" title="{{acc.email}}"></span>
			</div>
			<div class="col-xs-4">
					<span class="pull-right">
						<span ng-if="!acc.is_platform && $ctrl.canRegisterPlatform(acc) && acc.connected">
							<a class="btn btn-xs btn-success"
							   ng-click="$ctrl.showClienIdRegistrationForm($index)">Payouts</a>
						</span>
						<span class="text-success" ng-if="acc.connected">connected</span>
						<span class="text-danger" ng-if="acc.hidden">Deactivated</span>
						<button class="btn btn-xs btn-primary" ng-click="$ctrl.connect(acc)" ng-if="!acc.connected">Connect</button>
						<span uib-tooltip="Hide account from selection list"
							tooltip-append-to-body="true" class="fa fa-eye" 
							ng-click="$ctrl.toggleHidden(acc)" ng-if="acc.connected && !acc.hidden"></span>
						<span uib-tooltip="Show account from selection list"
							tooltip-append-to-body="true" class="fa fa-eye-slash" 
							ng-click="$ctrl.toggleHidden(acc)" ng-if="acc.connected && acc.hidden"></span>
					</span>
			</div>
		</div>
		<!-- Account alredy active for payouts -->
		<div class="row" ng-if="acc.is_platform">
			<div class="col-xs-8">
				<span class="label label-success" >This account is activated for Payouts</span>
			</div>
		</div>
		<!-- Input form for platform client ID -->
 		<div class="row" ng-if="$index == $ctrl.utils.registringAccountIndex && $ctrl.utils.showRegistrationInfo">
			<form class="form-horizontal col-xs-12" role="form" name="$ctrl.clientIDForm">
				<!-- Platform registation info and link -->
				<div>
					<p class="text payouts-text-info"> 
						<i class="fa fa-exclamation-triangle" aria-hidden="true"></i>
							To be able to make payouts, you have to activate your platform on the Account 
							you want to make payouts from in 
							<a 	ng-href="{{ $ctrl.getPlatformRegistrationURL() }}" 
								target="_blank" 
								class="pointer">Stripe account Dashboard</a>.
							After doing so, please, copy and paste your platform 
							<u>Production</u> <b>client_id</b> into the appropriate field and save. 
							You only need to do this once for each of your accounts.
					</p>
				</div>
				<div class="form-group validation-required">
						<div class="col-xs-7 col-md-9">
							<input 	type="text" 
										class="form-control" 
										placeholder="{{acc.title}}: Platform Client ID"
										name="client_id" 
										ng-model="acc.platform_client_id"
										ng-class="{'control-invalid' : $ctrl.clientIDForm.$invalid}"
										>
							<p class="text-danger text-xs" ng-if="$ctrl.clientIDForm.$invalid"> 
								<i class="fa fa-exclamation-triangle"></i>
								Client ID should be 35 characters long and should be of format: 
								ca_&ltcode&gt  e.g. &ltcode&gt 5p0r7wr3nch156r3475015I7RD3v734m
							</p>	
						</div>
						<div class="col-xs-5 col-md-3">
							<a class="btn btn-primary btn-block" type="button"
									ng-click="$ctrl.savePlatformClientId(acc)"
									ng-disabled="$ctrl.utils.savingInProcess"
									>Save Client ID</a>
						</div>
				</div>							
			</form>
		</div> 
    </li>
</ul>


