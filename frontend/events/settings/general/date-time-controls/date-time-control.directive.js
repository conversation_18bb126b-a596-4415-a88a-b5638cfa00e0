angular.module('SportWrench').directive('dateTimeFormControl', function () {
    return {
        restrict: 'E',
        scope: {
            date: '=',
            format: '@',
            fieldRequired: '=',
            name: '@',
            minDate: '=',
            maxDate: '=',
            change: '&onChange',
            deftime: '@',
            timepicker: '='
        },
        replace: true,
        templateUrl: 'events/settings/general/date-time-controls/date-time-control.html',
        link: function (scope) {
            scope.settings = {
                isOpen: false
            }
            scope.toggleOpen = function () {
                this.settings.isOpen = !this.settings.isOpen;
            }

            scope.disableTimepicker = typeof scope.timepicker === 'undefined' ? true : <PERSON><PERSON><PERSON>(scope.timepicker);
        }
    }
})
