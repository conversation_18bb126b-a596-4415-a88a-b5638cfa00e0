angular.module('SportWrench').service('eventUsersService',['$http', '$uibModal', eventUsersService]);

function eventUsersService($http, $uibModal) {
    this._$uibModal         = $uibModal;
	this._$http 			= $http;
	this._baseUrl 			= '/api/event/'; 
}

eventUsersService.prototype.getEventUsers = function (eventId) {
    return this._$http.get(this._baseUrl + eventId + '/users')
        .then(result => result.data);
};


eventUsersService.prototype.markAsDeleted = function(eventId, email) {
    return this._$http.delete(this._baseUrl + eventId +'/user', {data: {email}});
};

 // GET /api/event/:event/users/find?search_param=...
eventUsersService.prototype.findEligibleUsers = function (eventId, search) {
    return this._$http.get(this._baseUrl + eventId + '/users/find?search_param=' + search);
};

// 'POST /api/event/:event/user/add'	
eventUsersService.prototype.addEventUserByEmail = function(eventID, email, permissions) {
    return this._$http.post(this._baseUrl + eventID + '/user/add', { email, permissions })
};

eventUsersService.prototype.getUserPermissions = function () {
    return this._$http.get('/api/user-permissions').then(result => result.data && result.data.permissions);
};

eventUsersService.prototype.getUserPermissionsTree = function () {
    return this._$http.get('/api/user-permissions/tree').then(result => result.data && result.data.permissions);
}

eventUsersService.prototype.updateUserPermissions = function (eventID, email, permissions) {
    return this._$http.put(this._baseUrl + eventID + '/user', { email, permissions });
};

eventUsersService.prototype.openPermissionsEdit = function (eventID, user) {
    let self = this;

    return this._$uibModal.open({
        template:   ['<modal-wrapper>',
                    `<event-users-permissions 
                        user-data="user" 
                        move-back="moveBack()"
                        mode="edit"
                        user-permissions="user.permissions"
                        ></event-users-permissions>`,
                    `<external-button
                        class="btn btn-default pull-right"
                        ng-click="saveChanges()">Save
                     </external-button>`,
                    '</modal-wrapper>'].join(''),
        controller: ['$scope', '$uibModalInstance', function (scope, $uibModalInstance) {
            scope.modalTitle    = '<h4>Edit User Permissions</h4>';
            scope.user          = user;

            scope.moveBack      = function () {
                $uibModalInstance.close();
            };

            scope.saveChanges   = function () {
                let permissions = _.pick(scope.user.permissions, (item) => item === true);

                self.updateUserPermissions(eventID, user.email, permissions)
                    .then(() => {
                        $uibModalInstance.close({email: user.email, permissions: permissions});
                    })
            }
        }]
    }).result;
};
