angular.module('SportWrench').directive('imagesUploadForm', imagesUploadForm);

imagesUploadForm.$inject = ['fileUploadService'];

function imagesUploadForm(fileUploadService) {
    return {
        restrict: 'E',
        scope: {
            images: '=',
            filesToUpload: '=',
        },
        templateUrl: 'events/settings/general/forms/images-upload-form.html',
        replace: true,
        require: '^generalSettings',
        link: function (scope, attrs, elem, ctrl) {
            scope.data = {};
            scope.imagesData = [
                {
                    type: 'main-logo',
                    size: 5120,
                    title: 'Logo',
                },
                {
                    type: 'cover-image',
                    size: 5120,
                    title: 'Cover Image',
                },
            ];
            
            scope.$on('EventSettingsForm.Submitted', function () {
                ctrl.setImagesFormErrors([]);
            });

            var formName = 'images';

            ctrl.registerForm(formName);

            scope.$on('$destroy', function () {
                ctrl.unRegisterForm(formName);
            });
        },
        controller: ['$scope', function($scope) {}]
    }
}
