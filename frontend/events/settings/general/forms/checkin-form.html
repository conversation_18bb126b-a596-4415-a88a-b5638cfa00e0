<form name="teamsCheckInDetailsForm" class="form-horizontal">

    <div class="register-dates-teams col-sm-10 col-sm-offset-1">
        <div class="form-group">
            <label class="col-sm-4">On-line Team Check In Dates</label>
        </div>
        <div ng-class="{
            'form-group validation-required': true,
            'has-error': (utils.formSubmitted && teamsCheckInDetailsForm.online_team_checkin_start.$invalid) }
        ">
            <label class="control-label col-sm-2">Open Date</label>
            <div class="col-sm-5">
                <date-time-form-control
                    ng-class="{'form-control[disabled]': true}"
                    date="tournament.online_team_checkin_start"
                    format="MM/dd/yyyy HH:mm"
                    field-required="true"
                    max-date="tournament.date_start"
                    name="online_team_checkin_start"
                    required
                ></date-time-form-control>
            </div>
            <div ng-if="validators.online_team_checkin_start" class="col-sm-5 has-warning-div">
                <label ng-bind="validators.online_team_checkin_start"></label>
            </div>
        </div>
        <div ng-class="{
            'form-group validation-required': true,
            'has-error': (utils.formSubmitted && teamsCheckInDetailsForm.online_team_checkin_end.$invalid),
            'has-warning': (teamsDetailsForm.$error.online_team_checkin_end)}
        ">
            <label class="control-label col-sm-2">Close Date</label>
            <div class="col-sm-5">
                <date-time-form-control
                    ng-model="tournament.online_team_checkin_end"
                    date="tournament.online_team_checkin_end"
                    format="MM/dd/yyyy HH:mm"
                    field-required="true"
                    min-date="tournament.online_team_checkin_start"
                    max-date="tournament.date_end"
                    name="online_team_checkin_end"
                    time-validator
                    required
                ></date-time-form-control>
                <p class="help-block">Last date / time you want to allow teams to Check in On-Line</p>
            </div>
            <div ng-if="validators.online_team_checkin_end" class="col-sm-5 has-warning-div">
                <label ng-bind="validators.online_team_checkin_end"></label>
            </div>
        </div>
    </div>
    <div class="form-group"
         ng-class="{
            'form-group validation-required': true,
            'has-error': (utils.formSubmitted && teamsCheckInDetailsForm.online_team_checkin_mode.$invalid)
        }">
        <label class="col-sm-3 control-label">Check In Mode</label>
        <div class="col-sm-7">
            <select class="form-control"
                    ng-model="tournament.online_team_checkin_mode"
                    name="online_team_checkin_mode"
                    ng-change="onlineCheckinModeChanged()"
                    required
            >
                <option value="" selected>
                    Select Check-In Mode
                </option>
                <option value="default">
                    Send Team Check In QR Code to Staff selected to pick up wristbands on site
                </option>
                <option value="primary_staff_barcodes">
                    Send each Primary Staff a QR code for admission upon team check in
                </option>
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-offset-3 col-sm-9">
            <input type="checkbox" ng-model="tournament.teams_settings.allow_chaperone_qr_code"> Send QR codes for staff member with role = chaperone
        </label>
    </div>
    <div class="form-group" ng-if="tournament.teams_settings.allow_chaperone_qr_code">
        <label class="col-sm-3 control-label">QR code border</label>
        <div class="col-sm-2 radio">
            <label>
                <input type="radio" ng-model="tournament.teams_settings.chaperone_qr_code_border" ng-value="false">
                Without border
            </label>
        </div>
        <div class="col-sm-2 radio">
            <label>
                <input type="radio" ng-model="tournament.teams_settings.chaperone_qr_code_border" ng-value="true">
                Color border
            </label>
        </div>
    </div>
    <div class="form-group"
         ng-if="tournament.teams_settings.allow_chaperone_qr_code && tournament.teams_settings.chaperone_qr_code_border"
         ng-class="{
            'form-group validation-required': true,
            'has-error': (utils.formSubmitted && teamsCheckInDetailsForm.border_colour.$invalid)
        }">
        <label class="col-sm-3 control-label" for="chaperone_qr_code_border_color">QR code border color</label>
        <div class="col-sm-7">
            <select class="form-control"
                    name="border_colour"
                    ng-model="tournament.teams_settings.chaperone_qr_code_border_color"
                    required
            >
                <option value="" selected>Select color border</option>
                <option value="green">Green</option>
                <option value="yellow">Yellow</option>
                <option value="blue">Blue</option>
                <option value="red">Red</option>
                <option value="violet">Violet</option>
            </select>
        </div>
    </div>
    <div class="form-group">
    	<label class="control-label col-sm-3">Default authorization information that will appear on the On-Line Teams Check-in page</label>
    	<div class="col-sm-9">
    		<html-editor-panel
			    info="tournament.online_team_checkin_disclaimer"
			    modal-title="Default authorization information that will appear on the On-Line Teams Check-in page"
			>
    	</div>
    </div>
    <div class="form-group" ng-if="isPrimaryStaffBarcodesMode">
    	<label class="control-label col-sm-3">Information displayed to clubs in a modal window after teams complete Online Check-In</label>
    	<div class="col-sm-9">
    		<html-editor-panel
			    info="tournament.online_team_checkin_info"
			    modal-title="Information displayed to clubs in a modal window after teams complete Online Check-In"
			>
    	</div>
    </div>
</form>
