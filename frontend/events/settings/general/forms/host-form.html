<form name="hostForm" class="form-horizontal">
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && hostForm.org_name.$invalid) }">
        <label class="col-sm-3 control-label">Organization Name</label>
        <div class="col-sm-7">
            <input
                type="text" 
                class="form-control" 
                placeholder="Host Organization Name ..."
                ng-model="tournament.hosting_org_name"
                name="org_name"
                required>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && hostForm.host_address.$invalid) }">
        <label class="col-sm-3 control-label">Address</label>
        <div class="col-sm-7">
            <input 
                type="text" 
                class="form-control" 
                placeholder="Address ..."
                ng-model="tournament.hosting_org_address"
                name="host_address"
                required>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && hostForm.host_city.$invalid) }">
        <label for="host_city" class="col-sm-3 control-label">City</label>
        <div class="col-sm-7">
            <input 
                type="text"
                class="form-control" 
                placeholder="City ..."
                ng-model="tournament.hosting_org_city"
                name="host_city" 
                required>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && hostForm.host_state.$invalid) }">
        <label class="col-sm-3 control-label">State/Province</label>
        <div class="col-sm-4">
            <spinner active="utils.statesLoading"></spinner>
            <select 
                class="form-control" 
                ng-show="!utils.statesLoading"
                ng-model="tournament.hosting_org_state"
                name="host_state"
                ng-options="st.state as st.name for st in utils.states | orderBy:'name'"
                required>
                <option value="" selected>Select...</option>
            </select>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && hostForm.host_zip.$invalid) }">
        <label class="col-sm-3 control-label">ZIP</label>
        <div class="col-sm-7">
            <input
                type="text" 
                class="form-control" 
                placeholder="ZIP ..."
                ng-model="tournament.hosting_org_zip"
                name="host_zip"
                usa-zip
                required>
            <form-error-block ng-if="utils.formSubmitted && hostForm.host_zip.$error.usaZip" message="Host Zip should be a 5-digit Number"></form-error-block>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && hostForm.org_phone.$invalid) }">
        <label class="col-sm-3 control-label">Phone</label>
        <div class="col-sm-7">
            <input
                type="tel" 
                class="form-control"
                ui-mask="(*************"
                name="org_phone"
                ng-model="tournament.hosting_org_phone"
                required
                phone-validator
                >
            <form-error-block ng-if="utils.formSubmitted && hostForm.org_phone.$error.phone" message="Phone should be a 10-digit Number"></form-error-block>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && hostForm.website.$invalid) }">
        <label class="col-sm-3 control-label">Website URL</label>
        <div class="col-sm-7">
            <input 
                type="url" 
                class="form-control" 
                placeholder="http://website.com/"
                ng-model="tournament.website"
                name="website"
                required
                url-validator>
            <form-error-block ng-if="utils.formSubmitted && hostForm.website.$error.url" message="{{utils.invalid_url_message}}"></form-error-block>
        </div>
    </div>
    <div ng-class="{ 'form-group': true, 'has-error': (utils.formSubmitted && hostForm.rules_website.$invalid) }">
        <label class="col-sm-3 control-label">Rules Website URL</label>
        <div class="col-sm-7">
            <input 
                type="url" 
                class="form-control" 
                ng-model="tournament.rules_website"
                name="rules_website"
                placeholder="http://website.com/"
                url-validator>
            <form-error-block ng-if="utils.formSubmitted && hostForm.rules_website.$error.url" message="{{utils.invalid_url_message}}"></form-error-block>
            <p class="help-block">This URL will be sent by email to entered teams. Leave it empty to use Website URL.</p>
        </div>
    </div>
    <div ng-class="{ 'form-group validation-required': true, 'has-error': (utils.formSubmitted && hostForm.email.$invalid) }">
        <label class="col-sm-3 control-label">Email</label>
        <div class="col-sm-7">
            <input 
                type="email" 
                name="email" 
                ng-model="tournament.email"
                class="form-control"
                placeholder="<EMAIL>"
                autofill-sync
                email-validator
                required>
            <form-error-block ng-if="utils.formSubmitted && hostForm.email.$error.email" message="Invalid Email"></form-error-block>
        </div>
    </div>
    <div class="form-group">
        <label class="col-sm-3 control-label">Notify Frequency of Notifications</label>
        <div class="col-sm-4">
            <select class="form-control" ng-model="tournament.notify_frequency" name="frequency">
                <option value="never">Never</option>
                <option value="hourly">Hourly</option>
                <option value="daily">Daily</option>
                <option value="immediately">Immediately</option>
            </select>
            <p class="help-block">How often do you want to receive notifications of actions such as new entries</p>
        </div>
    </div>
    <div ng-class="{ 'form-group': true, 'has-error': (utils.formSubmitted && hostForm.notify_emails.$invalid) }" 
         ng-if="showNotifyEmailsBox()">
        <label class="col-sm-3 control-label">Notify emails</label>
        <div class="col-sm-7">
            <input 
                class="form-control"
                type="text"
                name="notify_emails"
                ng-model="tournament.notify_emails"
                ng-model-options="{ debounce: 500 }"
                notify-emails-validator>
            <form-error-block ng-if="utils.formSubmitted && hostForm.notify_emails.$error.emailsList" message="Invalid Email(s)"></form-error-block>
            <p class="help-block">Add emails of desired recipients of notifications. Can be a comma delimited list of multiple emails</p>
        </div>
    </div>
    <!-- SOCIAL NETWORKS LINKS -->
    <div 
        ng-repeat="(key, value) in utils.networks"
        ng-class="{ 'form-group': true, 'has-error': (utils.formSubmitted && hostForm[key].$invalid) }" >
        <label for="{{key}}" class="col-sm-3 control-label">
            <img ng-src="{{utils.img_path + value.img}}" alt="{{value.name}}" title="{{value.name}}" class="social-img">
        </label>
        <div class="col-sm-7">
            <input 
                type="text" 
                class="form-control"
                name="{{key}}" 
                ng-model="tournament.social_links[key]"
                social-links-validator="{{getDataForValidator(key, value.type)}}"
            >
            <form-error-block ng-if="utils.formSubmitted && hostForm[key].$error.social" message="{{socialValidatioMessage(value.type)}}"></form-error-block>
        </div>
    </div>
</form>
