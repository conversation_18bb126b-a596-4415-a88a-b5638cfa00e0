angular.module('SportWrench')
.directive('usaZip', usaZip)
.directive('zipValidator', zipValidator);

function usaZip() {
    return {
        require: 'ngModel',
        link: function (scope, elem, attrs, ctrl) {
            ctrl.$validators.usaZip = function (modelValue) {
                if (ctrl.$isEmpty(modelValue)) return true;
                if(/^([0-9]{5})$/.test(modelValue)) return true;
                return false;
            };
        }
    };
}

zipValidator.$inject = ['ZIP_REG_EXP'];

function zipValidator(ZIP_REG_EXP) {
    return {
        require: 'ngModel',
        link: function (scope, elem, attrs, ctrl) {
            ctrl.$validators.zipValidator = function (modelValue) {
                if (ctrl.$isEmpty(modelValue)) return true;
                if(ZIP_REG_EXP.test(modelValue)) return true;
                return false;
            };
        }
    };
}
