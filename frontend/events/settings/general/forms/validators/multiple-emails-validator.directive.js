angular.module('SportWrench').directive('multipleEmailsValidator', multipleEmailsValidator);

multipleEmailsValidator.$inject = ['EMAIL_PATTERN'];

function multipleEmailsValidator(EMAIL_PATTERN) {
    return {
        require: 'ngModel',
        link: function (scope, elem, attrs, ctrl) {
            ctrl.$validators.email = function (modelValue) {
                let isValid  = true;

                if (!ctrl.$isEmpty(modelValue)){
                    let emails = modelValue.split(', ');

                    let uniqueEmails = [];

                    emails.forEach(email => {
                        email = email.trim();

                        if(!EMAIL_PATTERN.test(email)){
                            isValid = false;
                        }

                        if(!uniqueEmails.includes(email)) {
                            uniqueEmails.push(email);
                        }
                    });

                    if(emails.length !== uniqueEmails.length) {
                        isValid = false;
                    }
                }

                return isValid;
            }
        }
    }
}
