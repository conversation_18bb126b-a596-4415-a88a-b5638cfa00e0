@import "modules/all";
@import "bootstrap-sass/assets/stylesheets/bootstrap/mixins";
@import "bootstrap-sass/assets/stylesheets/bootstrap/variables";

.form-login
{
   margin-top: 15px;

   .form-login__group
   {
      display: table;
      width: 100%;
      margin-bottom: 0;
      background-color: $gray-lighter;
      box-shadow: inset 0 0 3px $gray-dark;

      &:first-of-type
      {
         border-radius: 5px 5px 0 0;
      }

      &:last-of-type
      {
         border-radius: 0 0 5px 5px;
         margin-bottom: 50px;
      }
   }

   .form-control
   {
      background: transparent;

      display: table-cell;
      padding-left: 0;
      border: 0;
      border-radius: 3px;
      @include placeholder($gray-dark);

      &:focus
      {
         outline: none;
         border: 0;
         box-shadow: none;
      }
   }

   .fa
   {
      display: table-cell;
      vertical-align: middle;
      padding-left: 10px;
      color: $black-pure;
   }
}

.form-registration
{
   input[type="submit"]
   {
      margin-top: 10px;
   }
}
