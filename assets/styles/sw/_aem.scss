.modal-xxl {
  width: 98%;
  min-width: 1000px;
}

.aem {
  .tmpls-list {
    .list-group-item {
        @media screen and (max-width: 767px) {
          span {
            display: block;
          }
        }

        @media screen and (min-width: 768px) {
          span {
            display: inline;
          }
        }
    }
  }
}

.tmpls-list .xs-aem-btn {
  @media (max-width: 480px) {
    .btn-default, .btn-primary, .btn-danger {
      width: 49%;
    }
    .btn-success, .btn-info {
      width: 100%;
    }
    span.inline-block {
      width: 49%;
    }
    .pull-right {
      display: block;
      float: none !important;
    }
  }
}

.react-email-editor-modal {
    .modal-dialog {

      height: 100%;
      min-height: 100%;
      margin-top: 0px;
      margin-bottom: 0px;

        .modal-content {
            position:relative;
            height: auto;
            min-height: 100%;
            
            /* In case somebody tries to add a footer */
            .modal-footer {
              display: none;
            }
        }
    }

    .template-updating.modal-body {
        position: absolute;
        top: 0px;
        bottom: 0;
        left: 0;
        width: 100%;
        min-height: 100%;
        height: auto;
        padding: 5px 0 0 0; 
    }
}

.react-email-editor-container {
    overflow: hidden;
    width: 99.9%;
    margin: 0 auto;
    position: relative;
    border-radius: 0 0 6px 6px;
    -webkit-border-radius: 0 0 6px 6px;
    -moz-border-radius: 0 0 6px 6px;
}

.aem-basic-list {
  .picked {
    -webkit-box-shadow: 0px 0px 10px 3px rgba(51,153,243,0.75);
    -moz-box-shadow: 0px 0px 10px 3px rgba(51,153,243,0.75);
    box-shadow: 0px 0px 10px 3px rgba(51,153,243,0.75);
    border: 0;
  }

  .not-picked {
    -webkit-box-shadow: 0px 0px 10px 3px rgba(242,222,222,0.75);
    -moz-box-shadow: 0px 0px 10px 3px rgba(242,222,222,0.75);
    box-shadow: 0px 0px 10px 3px rgba(242,222,222,0.75);
    border: 0;
  }
}

.aem-tmpl-updating-form {
    .type-transclusion {
        h1, h2, h3, h4, h5, h6 {
          margin: 0;
        }
    }

    .subject-control {
        [class^="col-xs-"] {
            padding: 0px;
        }
        [class^="col-xs-"]:first-child {
            padding-left: 15px;
        }
        [class^="col-xs-"]:last-child {
            padding-right: 15px;
        }
    }

    .tmpl-title {
        margin: 0;
    }
}

.aem-tmpl-preview {
    display: block;
    width: 100%;
}

.list-group-item.disabled, .list-group-item.disabled:hover {
    color: #999;
}

.campaigns-table-header {
    font-size: 16px;
    border: none !important;
    font-weight: 500;
}

.mass-send {
    &-wrapper {
        display: flex;
        justify-content: center;
        margin-top: 10px;
        flex-direction: column;
    }

    &_dropdown-wrapper {
        display: flex;
        justify-content: space-between;
    }

    &_text-field-wrapper {
        display: flex;
        justify-content: space-between;
        margin: 15px 0;

        .btn-group {
            padding: 0;
        }
    }

    &_events-btn {
        width: 100%;
    }

    &_groups-btn {
        width: 100%;
    }

    &_reply-to-wrapper {
        display: flex;
        align-items: center;
        padding: 0;

        .control-label {
            padding: 0;
        }
    }

    &_save-btn-wrapper {
        display: flex;
        flex-direction: row-reverse;
    }

    &_dropdown-menu {
        max-height: 400px;
        overflow: scroll;
    }
}

.duplicate-template {
    &_current-event-row {
        margin-bottom: 15px;
    }
}

