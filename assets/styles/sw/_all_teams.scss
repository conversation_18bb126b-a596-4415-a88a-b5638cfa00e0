.event-all-teams-table {
    td:not(.text-right):not(.text-left) {text-align: center;}
}

.loading-container {
    position: relative;
}
.loading-container .loading:before, .loading-container .loading:after {
    content: " ";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    min-height: 50px;
    min-width: 50px;
}
.loading-container .loading:before, 
input[name="all_teams_search"]:disabled, 
input[name="all_teams_search"]:read-only {
    background-color: white;
    opacity: 0.5;
}

.q-ty, .q-ty:hover {
    text-decoration: none;
}

.narrow-form-control {
    padding-left: 4px;
    padding-right: 4px;
}

.filters-panel {
    margin-bottom: 15px;

    // .multiselect-filter {
    //     width: 25%;
    // }

    @media screen and (min-width: 1200px) {
        >[class^="col-lg-"]:first-child {
            padding-right: 3px;
        }   

        >[class^="col-lg-"]:not(:last-child):not(:first-child) {
            padding-left: 3px;
            padding-right: 3px;
        }   

        >[class^="col-lg-"]:last-child {
            padding-left: 3px;
        }

        .ctrl-btn {
            padding-left: 6px;
            padding-right: 6px;
        }
    }

    @media screen and  (min-width: 768px) {
        .form-inline {
            display: inline-flex;
            display: -webkit-inline-flex;

            -webkit-justify-content: center;
            justify-content: center;

            flex-wrap: wrap;
            -webkit-flex-wrap: wrap;

            width: 100%;
        }
    }

    @media screen and (min-width: 992px) {
        .form-inline {
            justify-content: space-between;
            -webkit-justify-content: space-between;
        }
    }

    @media screen and (max-width: 1199px) {
        .ctrl-btn {
            display: block !important;
            margin-top: 3px;
            margin-bottom: 3px;
        }
    }

}

.form-group--custom {
    position: relative;

    .refresh {
        margin-left: 10px;
    }

    .help-block, .btn {
        position: absolute;
        bottom: -18px;
        top: auto;
        right: auto;
        left: 0;
        margin: 0 !important;
        width: 250px;
        color: #000 !important;
        overflow: visible;
    }

    @media screen and (max-width: 767px) {
        .help-block {
            position: static !important;
        } 
    }
}

.field-old-val {
    padding-left: 18px; 
    margin: 0; 
    cursor: pointer
}

// this z-index must be more than z-index in footer.
.drowdown-zindex {
    z-index: 1035;
}

.t-error {
    color: red;
}

.division-table-edit-icon {
    padding-top: 3px;
}

.division-table-body {
    & > tr {
        & > td {
            &:first-child {
                padding-bottom: 15px;
            }
        }
    }
}

.team-qualification {
    &_title {
        margin-bottom: 2px;
    }

    &_footer {
        margin-top: 25px;
    }

    &_success-message {
        margin-top: 10px;
    }
}

.online-staffers-list {
    padding: 15px;
}

.online-checkin-button-wrapper-line {
    padding: 0;
    margin-top: 5px;
}
