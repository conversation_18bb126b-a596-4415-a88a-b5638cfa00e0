!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e||self).paymentHubSdk={})}(this,function(e){var t="0.0.1",n={},i=1e6*Math.random();window.addEventListener("message",function(e){if(e.data.type){var t=Object.entries(n).find(function(t){var n=t[0];return n.includes(e.origin)&&e.data.src&&n.includes(e.data.src)});if(t){var i="data"in e.data?new CustomEvent(e.data.type,{detail:e.data.data}):new Event(e.data.type);t[1].dispatchEvent(i)}}},!1),e.createPaymentForm=function(e){var t;if("elementId"in e?t=document.getElementById(e.elementId):"element"in e&&(t=e.element),t){if("IFRAME"!==t.nodeName)throw new TypeError("Element must be iframe")}else t=document.createElement("iframe"),"elementId"in e&&(t.id=e.elementId),"appendTo"in e&&e.appendTo.appendChild(t);return t.setAttribute("sandbox","allow-top-navigation allow-scripts allow-same-origin allow-forms"),t.setAttribute("allow","payment *"),t.setAttribute("src",e.src),t.setAttribute("frameBorder","0"),["width","height"].forEach(function(n){n in e&&(t.style[n]=e[n])}),n[e.src]=t,t},e.pay=function(e){return function(e,n,a){var o=++i,r=!1;return new Promise(function(i,a){e.addEventListener("response:"+n+":"+o,function t(d){r=!0,d.detail&&d.detail.received||(d.detail&&d.detail.error?a(d.detail):i(d.detail),e.removeEventListener("response:"+n+":"+o,t))}),setTimeout(function(){r||a({error:!0,noAck:!0,message:"Not delivered"})},3e4),e.contentWindow.postMessage({src:window.location.href,version:t,type:n,data:void 0,counter:o},"*")})}(e,"payment:pay")},e.version=t});
//# sourceMappingURL=index.umd.js.map
