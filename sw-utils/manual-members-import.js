'use strict';

const argv  = require('optimist').argv;
const fs    = require('fs').promises;
const xlsx  = require('xlsx');
const _  = require('lodash');
global._ = require('lodash');
global.squel = require('../api/services/squel');
global.knex = require('knex')({client: 'pg'});
global.sails = {
    config: {
        sw_season: require('../config/sw_season').sw_season,
    },
};
global.EventTeamService = require('../api/services/EventTeamService');

const {
    event: EVENT_ID,
    path: PATH_TO_FILE,
    conn: DB_CONNECTION,
} = argv;

const DOCUMENT_HEADING = {
    TEAM_NAME: 'Team Name',
    EMAIL : 'Email',
    FIRST: 'First Name',
    LAST: 'Last Name',
};

const connectionConfig = JSON.parse(Buffer.from(DB_CONNECTION, 'base64').toString('utf-8'));

const connectionParams = _.isObject(connectionConfig)
    ? _.omit(connectionConfig, 'module')
    : {connectionString: connectionConfig};
const db = require('../api/lib/db.js');
global.Db = new db(connectionParams, {}, {error: console.error});

async function processImport() {
    try {
        const teamIDMap = await buildTeamsIDs(EVENT_ID);

        const file = await fs.readFile(PATH_TO_FILE);
        let parsedRows = parseFile(file, teamIDMap);

        if(!parsedRows.length) {
            throw new Error('Empty list passed');
        }

        await EventTeamService.manual_roster_addition.addMembersManually(parsedRows);

        return { parsed: parsedRows.length };
    } catch (err) {
        throw err;
    }
}

async function buildTeamsIDs(eventID) {
    const { rows } = await Db.query(
        knex('roster_team AS rt')
            .join('master_team AS mt', 'mt.master_team_id', 'rt.master_team_id')
            .select(['mt.master_team_id', 'rt.team_name', 'rt.roster_team_id'])
            .where('rt.event_id', eventID)
    );

    const teamIDMap = new Map();
    for(const { roster_team_id, master_team_id, team_name } of rows) {
        if(teamIDMap.has(team_name)) {
            throw { validation: `Event has multiple teams with name "${team_name}"` };
        }

        teamIDMap.set(team_name, { roster_team_id, master_team_id });
    }

    return teamIDMap;
}

function parseFile(file, teamsIDsMap) {
    let workbook = xlsx.read(file);

    let workingSheet = workbook.Sheets[workbook.SheetNames[0]];
    let rawJSON      = xlsx.utils.sheet_to_json(workingSheet, { header: 1 });

    return formatRows(rawJSON, teamsIDsMap);
}

function formatRows (parsedFile, teamsIDsMap) {
    let headings = parsedFile[0];

    const columnsConfig = [
        {name: 'teamName', title: DOCUMENT_HEADING.TEAM_NAME, required: true},
        {name: 'email', title: DOCUMENT_HEADING.EMAIL, required: true},
        {name: 'first', title: DOCUMENT_HEADING.FIRST, required: false},
        {name: 'last', title: DOCUMENT_HEADING.LAST, required: false},
    ].reduce((config, columnConfig) => {
        const index = headings.indexOf(columnConfig.title);
        if(index === -1) {
            if(columnConfig.required) {
                throw new Error(`"${columnConfig.title}" column not found`);
            }
            else {
                return config;
            }
        }
        config.push({
            ...columnConfig,
            index,
        });
        return config;
    }, []);

    const parseRow = (row, index) => {
        return columnsConfig.reduce((result, columnConfig) => {
            let value = row[columnConfig.index];
            if(!value) {
                if(columnConfig.required) {
                    throw {validation: `"${columnConfig.title}" column value is required on row ${index+1}`};
                }
                else {
                    result[columnConfig.name] = null;
                    return result;
                }
            }
            result[columnConfig.name] = value.trim();

            return result;
        }, {});
    };

    return parsedFile.reduce((all, row, index) => {
        const isHeadings = index === 0;
        const rowHasValues = row.some(v => Boolean(v));
        if(!isHeadings && rowHasValues) {

            const {
                teamName,
                email,
                first,
                last
            } = parseRow(row, index);

            const teamIDs = teamsIDsMap.get(teamName);

            if(_.isEmpty(teamIDs)) {
                throw { validation: `Team "${teamName}" not found on event` };
            }

            all.push({
                email: email,
                first: first || teamName,
                last: last || teamName,
                staff_role: 4 /* Head Coach */,
                type: 'staff',
                roster_team_id: teamIDs.roster_team_id,
                master_team_id: teamIDs.master_team_id
            });
        }

        return all;
    }, [])
}

processImport()
    .then(result => {
        process.stdout.write(JSON.stringify(result));
        process.exit(0);
    })
    .catch(err => {
        Db.end();
        console.error(JSON.stringify(err));

        process.exit(1);
    });
