/**
* 
* Tickets discounts xlsx parser.
*
*/

'use strict';

const 
    fs      = require('fs'),
    path    = require('path'),
    argv    = require('optimist').argv,
    co      = require('co'),
    xlsx    = require('xlsx'),
    pg      = require('pg'),
    crypto  = require('crypto'),
    Logger  = require('./log'),
    log     = new Logger();

const 
    DISCOUNT_TYPES = {
        COUPON      : 'COUPON',
        EM2FREE     : 'EM2_FREE'
    }

const 
    UPLOAD_PATH         = argv.path,
    EVENT_ID            = Number(argv.event),
    TICKET_ID           = Number(argv.ticket),
    AMOUNT              = Number(argv.amount),
    CONNECTION          = argv.conn,
    SKIP_FIRST_LINE     = (argv.skip_first === 'true'), 
    DISCOUNT_TYPE       = argv.type || DISCOUNT_TYPES.COUPON 

if(!UPLOAD_PATH) {
    console.error('Invalid Upload Path')
    process.exit(1);
}

if(!EVENT_ID) {
    console.error('Invalid Event Identifier')
    process.exit(1);
}

if(!TICKET_ID) {
    console.error('Invalid Event Ticket Identifier')
    process.exit(1);
}

if((DISCOUNT_TYPE === DISCOUNT_TYPES.COUPON) && !AMOUNT) {
    console.error('Invalid Amount')
    process.exit(1);
}

if(!CONNECTION) {
    console.error('Invalid Connection String')
    process.exit(1);
}

co(function* () {
    let fileBuffer = yield (new Promise((resolve, reject) => {
        log.debug(path.resolve(UPLOAD_PATH, `DISCOUNTS-${new Date().getTime()}-${EVENT_ID}.xlsx`))
        let outputStream = fs.createWriteStream(
                                path.resolve(UPLOAD_PATH, `DISCOUNTS-${new Date().getTime()}-${EVENT_ID}.xlsx`)),
            inputStream  = fs.createReadStream(null, { fd: 0 }),
            chunks       = [],
            totalLength  = 0 

        inputStream.on('data', chunk => {
            chunks.push(chunk);
            totalLength += chunk.length;
        });

        inputStream.on('close', () => {
            if(totalLength === 0) {
                reject({ validation: 'Empty File passed', statusCode: 2 })
            } else {
                resolve(Buffer.concat(chunks, totalLength))
            }
        })

        inputStream.on('error', err => {
            reject(err);
        })

        inputStream.pipe(outputStream);
    }));

    log.debug('Connecting to DB ...');
    let dbClient = new pg.Client(JSON.parse(Buffer.from(CONNECTION, 'base64').toString('utf-8')));
    dbClient.connect();

    let workbook = xlsx.read(fileBuffer);
    let workingSheet = workbook.Sheets[workbook.SheetNames[0]];
    // https://github.com/SheetJS/js-xls/issues/42
    let jsonSheetRows = xlsx.utils.sheet_to_json(workingSheet, {
                            header: 1
                        });

    if(!jsonSheetRows.length) {
        throw { validation: 'Empty File passed', statusCode: 2 }
    }

    if(SKIP_FIRST_LINE) {
        jsonSheetRows.shift();
    }

    yield runSQL(dbClient, 'BEGIN');

    let modified;
    if(DISCOUNT_TYPE === DISCOUNT_TYPES.COUPON) {
        modified = yield jsonSheetRows.reduce((prev, current) => {
           return prev.then(modifiedRowsSum => {
               return upsertDiscount(dbClient, current)
               .then(modifiedQty => {
                   return (modifiedQty + modifiedRowsSum);
               })
           })
        }, Promise.resolve(0))
    } else {
        modified = yield Promise.all(
            jsonSheetRows.map((discountRow) => {
                if(discountRow && discountRow.length) {
                    return upsertFreeTicket(dbClient, discountRow);
                } else {
                    return 0;
                }
            })
        );
        modified = modified.reduce((sum, current) => { return (sum + current) })
    }

    yield runSQL(dbClient, 'COMMIT');

    return modified;
}).then(modifiedRowsQty => {
    log.debug('Modified', modifiedRowsQty);
    process.exit(0)
}).catch(err => {
    console.error(!!err.validation?err.validation:err);
    process.exit(err.statusCode || 1)
});

function generateUniqueHash (client) {
    let hash = crypto.randomBytes(8).toString('hex').toUpperCase();
    return runSQL(
        client,
        'SELECT "ticket_discount_id" FROM ticket_discount WHERE "code" = $1 and "event_id" = $2',
        [hash, EVENT_ID]
    ).then(result => {
        if(result.rows.length > 0) {
            return generateUniqueHash(client);
        } else {
            return hash;
        }
    })
}

function upsertFreeTicket (client, discount) {
    let reservationId   = discount[0],
        first           = discount[1],
        last            = discount[2],
        email           = discount[3];

    if(!(reservationId && last && email)) {
        return Promise.reject(`Invalid row ${JSON.stringify(discount)}`)
    }

    if(email.indexOf('@') < 0) {
        return Promise.reject(`Invalid Email "${email}"`)
    }

    return runSQL(
        client,
        `INSERT INTO "ticket_discount" (  
             "email", "first", "last", "event_id", "event_ticket_id", "max_count", "discount", "code"
         ) 
         SELECT LOWER(TRIM(($1)::TEXT)), LOWER(TRIM(($2)::TEXT)), LOWER(TRIM(($3)::TEXT)), $4, $5, $6, $7, $8 
         WHERE NOT EXISTS (
            SELECT "ticket_discount_id" FROM "ticket_discount" 
            WHERE "event_id" = $4 
                AND LOWER(TRIM("code")) = LOWER(TRIM($8))
         )
         RETURNING "ticket_discount_id"`,
        [email, first, last, EVENT_ID, TICKET_ID, 1, -1, reservationId]
    ).then(result => {
        return result.rows.length;
    })
}

function upsertDiscount (client, discount) {
    let email   = discount[0],
        first   = discount[1],
        last    = discount[2],
        qty     = Number(discount[3]);

    if(!(email && qty) || qty < 0) {
        return Promise.reject(`Invalid row ${JSON.stringify(discount)}`)
    }

    if(email.indexOf('@') < 0) {
        return Promise.reject(`Invalid Email "${email}"`)
    }

    return generateUniqueHash(client)
    .then(coupon => {
        return runSQL(
            client, 
            `INSERT INTO "ticket_discount" (  
                 "email", "first", "last", "event_id", "event_ticket_id", "max_count", "discount", "code"
             ) 
             SELECT LOWER(TRIM(($1)::TEXT)), LOWER(TRIM(($2)::TEXT)), LOWER(TRIM(($3)::TEXT)), $4, $5, $6, $7, $8 
             RETURNING "ticket_discount_id"`,
            [email, first, last, EVENT_ID, TICKET_ID, qty, AMOUNT, coupon]
        )
    }).then(result => {
        return result.rows.length;
    })
}

function runSQL (client, text, params) {
    return new Promise((resolve, reject) => {
        client.query(text, params, function (err, result) {
            if(err) {
                err.sql = text;
                err.params = params;
                reject(err);
            } else {
                resolve(result);
            }
        })
    })
}
