'use strict';

const 
	argv 	= require('optimist').argv,
	xlsx 	= require('xlsx'),
	pg      = require('pg'),
	Logger 	= require('./log'),
	log 	= new Logger();

const { formatSheetName } = require('./export-helper');

const
	EVENT_ID 			= Number(argv.event),
	DB_CONNECTION_STR 	= argv.connection,
	OUTPUT_FILEPATH 	= argv.path,
	COLUMNS_LENGTH 		= 4;

let connectionParams;

try {
	connectionParams = JSON.parse(Buffer.from(DB_CONNECTION_STR, 'base64').toString('utf-8'));
} catch (e) {
	console.error(e);
	process.exit(1)
}

log.debug('Connecting to the DB');
let dbClient = new pg.Client(connectionParams);
dbClient.connect();

new Promise((resolve, reject) => {
	dbClient.query(
		`SELECT
		     e.long_name "event_name", e.name "short_name", (
		         SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("d"))), '[]'::JSON)
		         FROM (
		             SELECT d.name, d.short_name, (
		                 SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("s"))), '[]'::JSON)
		                 FROM (
							SELECT 
								COALESCE(ds.rank, 0) "rank", rt.team_name, rt.organization_code "team_code"
							FROM "roster_team" rt 
							LEFT JOIN "division_standing" ds
								ON rt.roster_team_id = ds.team_id
							WHERE rt.event_id = e.event_id 
								AND rt.division_id = d.division_id
								AND rt.status_entry = 12 
								AND rt.deleted IS NULL
		                      ORDER BY ds.rank ASC, rt.organization_code
		                 ) "s"
		             ) "standings"
		             FROM "division" d 
		             WHERE d.event_id = e.event_id
		             ORDER BY d.sort_order, d.gender, d.max_age desc, d.level_sort_order, d.level
		         ) "d"
		     ) "divisions"
		 FROM "event" e 
		 WHERE e.event_id = $1`,
        [EVENT_ID],
        (err, result) => {
        	if(err) {
        		reject(err);
        	} else {
        		resolve(result.rows[0] || {})
        	}
        }
    )
}).then(eventData => {
	log.debug('Got data from DB.');
	let sheet 		= {},
		totalRows 	= 2;


	sheet[xlsx.utils.encode_cell({ c: 0, r: 0 })] = { v: eventData.event_name, t: 's' };

	eventData.divisions.forEach(d => {
		sheet[xlsx.utils.encode_cell({ c: 0, r: totalRows })] = { v: d.short_name, 	t: 's' };
		sheet[xlsx.utils.encode_cell({ c: 0, r: totalRows })] = { v: d.name, 		t: 's' };
		totalRows += 2;

		d.standings.forEach(ds => {
			addEmptyCell(sheet, 0, totalRows);
			sheet[xlsx.utils.encode_cell({ c: 1, r: totalRows })] = { v: orderPostfix(ds.rank) 	|| 0, 	t: 's' };
			sheet[xlsx.utils.encode_cell({ c: 2, r: totalRows })] = { v: ds.team_name 			|| '', 	t: 's' };
			sheet[xlsx.utils.encode_cell({ c: 3, r: totalRows })] = { v: ds.team_code 			|| '', 	t: 's' };
			totalRows++;
		});
		totalRows++;
	})

	log.debug('Total rows', totalRows)

	sheet['!ref'] = xlsx.utils.encode_range({
		s: { c: 0, r: 0 }, 
		e: { c: COLUMNS_LENGTH, r: totalRows }
	})

	return {
		name 		: formatSheetName(`${eventData.short_name} $1`, ['USAV']),
		content 	: sheet
	};
}).then(sheetData => {
	log.debug('Sheet Generated')
	let workbook = {
		SheetNames 	: [sheetData.name],
		Sheets 		: {}
	};

	workbook.Sheets[sheetData.name] = sheetData.content;
	// writing option used in xlsx is 'w' 
	// 			 -  Open file for writing. The file is created (if it does not exist) or truncated (if it exists).
	xlsx.writeFile(workbook, OUTPUT_FILEPATH,  { font: { name: "Verdana" }, bookSST: true });
	log.debug('WorkBook written')
}).then(() => {
	process.exit(0)
}).catch(err => {
	log.error(err);
	process.exit(1);
})

function addEmptyCell (sheet, col, row) {
	sheet[xlsx.utils.encode_cell({ c: col, r: row })] = { v: '', t: 's' };
}

function orderPostfix(n) {
   let s = ['th', 'st', 'nd', 'rd'],
       v = (n % 100);

   return n + (s[(v - 20) % 10] || s[v] || s[0]);
}
