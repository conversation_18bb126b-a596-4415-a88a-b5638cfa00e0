'use strict';

const assert 			= require('assert');
const _ 				= require('lodash');
const pg 				= require('pg');
const wpDataReceiver 	= require('../api/services/WebpointDataReceiver');
const wpUtils 			= require('../api/services/WebpointUtility')

const DB_CONN = process.argv[2];
const SEASON  = Number(process.argv[3]);

let minYear   = new Date().getUTCFullYear() - 1;


assert(_.isString(DB_CONN), 'Connection should be a string');
assert(_.isNumber(SEASON), 'Season should be a number');
assert(SEASON >= minYear, `Season should be >= ${minYear}`);


const db 		    = new pg.Client(DB_CONN);

/* === find rows === */
function findInvalidMasterStaffRows (db, season, offset, limit) {
	return db.query(
		`SELECT 
		     ms."master_staff_id", ms."usav_number", 
		     ms."organization_code", wa."member_id", ms."last",
		     ms."master_club_id"
		 FROM "master_staff" ms
		 INNER JOIN "webpoint_adult" wa 
		     ON wa."usav_code" = ms."organization_code"
		     AND (wa."member_id" <> ms."usav_number" OR ms."usav_number" IS NULL)
		     AND wa."master_club_id" = ms."master_club_id"
		 WHERE ms."season" = $1 
		     AND ms."deleted" IS NULL
		 LIMIT ${limit} OFFSET ${offset}`,
		[season]
	).then(res => res.rows);
}

function findInvalidMasterAthleteRows (db, season, offset, limit) {
	return db.query(
		`SELECT 
		     ma."master_athlete_id", ma."usav_number", ma."organization_code",
		     ma."master_club_id",
		     wa."member_id", ma."last"
		 FROM "master_athlete" ma 
		 INNER JOIN "webpoint_athlete" wa 
		     ON wa."usav_code" = ma."organization_code"
		     AND (wa."member_id" <> ma."usav_number" OR ma."usav_number" IS NULL)
		     AND wa."master_club_id" = ma."master_club_id"
		 where ma."season" = $1  
		     AND ma."deleted" IS NULL
		 LIMIT ${limit} OFFSET ${offset}`,
		[season]
	).then(res => res.rows);
}



/* === get wp data === */
function getWPData (rows) {
	let receive 		= memberID => wpDataReceiver.v2.getMember(memberID);
	let validateResp 	= (row, resp) => {
		let member 	= resp.MEMBER_DATA.MEMBER;
		let orgCode = member.USAVNUMBER;		

		if (!_.isString(orgCode) || orgCode.length === 0) {
			throw new Error(`Empty USAV Code. Member ID ${member.ID}`)
		}

		orgCode = orgCode.trim();

		let usavNum = wpUtils.getMembershipNumber(orgCode);

		if (_.isNull(usavNum)) {
			throw new Error(`Invalid USAV Code format. Code: ${orgCode}, Member ID: ${member.ID}`)
		}

		if (usavNum.toString() !== member.ID.toString()) {
			throw new Error(`Member ID "${member.ID}" does not match USAV Number ${usavNum}`)
		}

		if (row.last.trim().toLowerCase() !== member.LAST_NAME.trim().toLowerCase()) {
			throw new Error(`Last name "${row.last} does not match "${member.LAST_NAME}". Member ID: ${member.ID}`)
		}

		return { orgCode, usavNum };
	}

	return Promise.all(
		rows.map(member => {
			return receive(member.member_id)
			.then(resp => {
				let { orgCode, usavNum } = validateResp(member, resp);

				if (orgCode.toLowerCase() !== member.organization_code.trim().toLowerCase()) {
					member.old_code 		 = member.organization_code
					member.organization_code = orgCode;
					member.usav_number 		 = usavNum;

					return member;
				} else {
					console.error(
						new Error(`USAV Codes do not match: "${
							orgCode
						}" "${member.organization_code}". Member ID: ${member.member_id}`)
					);

					return null;
				}
			})
			.catch(err => {
				console.error(err);

				return null;
			})
		})
	).then(result => result.filter(item => (item !== null)))
}

/* === update rows === */
function getTotalUpdQty (updRows) {
	return updRows.reduce((sum, current) => {

		if (_.isNumber(current)) {
			sum += current;
		}

		return sum;
	}, 0)
}

function updateMasterAthleteRows (db, rows) {
	let updMasterAthleteRow = athlete => {
		return db.query(
			`UPDATE "master_athlete" ma 
			 SET "organization_code" = $1,
			 	 "usav_number" = $2
			 WHERE ma."master_athlete_id" = $3
			 	AND ma."master_club_id" = $4`, [
			 	athlete.organization_code, 
			 	athlete.usav_number,
			 	athlete.master_athlete_id,
			 	athlete.master_club_id
			]
		)
	}

	let updWebpointAthleteRow = athlete => {
		return db.query(
			`UPDATE "webpoint_athlete" wa 
			 SET "usav_code" = $1
			 WHERE "usav_code" = $2
			 	AND NOT EXISTS (
					SELECT 1 FROM "webpoint_athlete" 
					WHERE "usav_code" = $1 AND "master_club_id" = $3
			    )
			    AND wa."master_club_id" = $3`,
			[athlete.organization_code, athlete.old_code, athlete.master_club_id]
		)
	}


	return db.beginTransaction().then(() => {
		return Promise.all(
			rows.map(athlete => 
				Promise.all([
					updMasterAthleteRow(athlete),
					updWebpointAthleteRow(athlete)
				]).then(res => res[0].rowCount)
			)
		)
	}).then(getTotalUpdQty)
	.then(result => {
		return db.query('COMMIT').then(() => result)
	})
	.catch(err => {
        db.query('ROLLBACK');

		return Promise.reject(err);
	})
}

function updateMasterStaffRows (db, rows) {
	let updMasterStaffRow = staffer => {
		return db.query(
			`UPDATE "master_staff" ms 
			 SET "organization_code" = $1,
			 	 "usav_number" = $2
			 WHERE ms."master_staff_id" = $3 
			 	AND ms."master_club_id" = $4`, [
			 	staffer.organization_code,
			 	staffer.usav_number,
			 	staffer.master_staff_id,
			 	staffer.master_club_id
			]
		)
	}

	let updWebpointAdultRow = staffer => {
		return db.query(
			`UPDATE "webpoint_adult" wa 
			 SET "usav_code" = $1
			 WHERE "usav_code" = $2
			 	AND NOT EXISTS (
					SELECT 1 FROM "webpoint_adult" 
					WHERE "usav_code" = $1 AND "master_club_id" = $3
			    )
			    AND wa."master_club_id" = $3`,
			[staffer.organization_code, staffer.old_code, staffer.master_club_id]
		)
	}

	return db.query('BEGIN').then(() => {
		return Promise.all(
			rows.map(staffer => {
				Promise.all([
					updMasterStaffRow(staffer),
					updWebpointAdultRow(staffer)
				]).then(res => res[0].rowCount)
			})
		)
	})
	.then(getTotalUpdQty)
	.then(result => {
		return db.query('COMMIT').then(() => result)
	})
	.catch(err => {
        db.query('ROLLBACK');

		return Promise.reject(err);
	})
}


/* === process rows === */
function processRows (findFn, updFn, db, season) {
	return findAndUpdateRows(0, 0, findFn, updFn, db, season);
}

function findAndUpdateRows (
					totalRowsQty, totalUpdQty, findFn, updFn, db, season, offset = 0, limit = 50) {
	
	return findFn(db, season, offset, limit)
	.then(rows => {
		let keepOnIteration = false;

		totalRowsQty += rows.length;

		if (rows.length === 0) {
			return keepOnIteration;
		} else {
			keepOnIteration = true;

			return getWPData(rows)
			.then(received => {
				console.log('  	Got', received.length, 'to update');
				return updFn(db, received)
			}).then(updatedQty => {
				totalUpdQty += updatedQty;

				return keepOnIteration;
			}).catch(err => {
				console.error(err);

				return keepOnIteration;
			})
		}
	}).then(keepOnIteration => {
		if (keepOnIteration) {

			offset += limit;

			return findAndUpdateRows(totalRowsQty, totalUpdQty, findFn, updFn, db, season, offset)
		} else {
			return {
				total 	: totalRowsQty,
				updated : totalUpdQty
			}
		}
	})

}



/* === main === */
db.connect()
.then(() => {
	console.log('Start Athletes processing');
	return processRows(findInvalidMasterAthleteRows, updateMasterAthleteRows, db, SEASON)
	.then(res => {
		console.log();
		console.log();
		console.log('Athletes processing finished. Updated', res.updated, 'of', res.total);
		console.log();
		console.log();
	})
})
.then(() => {
	console.log('Start Staff processing');
	return processRows(findInvalidMasterStaffRows, updateMasterStaffRows, db, SEASON)
	.then(res => {
		console.log();
		console.log();
		console.log('Staff processing finished. Updated', res.updated, 'of', res.total);
		console.log();
		console.log();
	})
})
.then(() => db.end().then(() => {
	console.log('Disconnected from DB Server');
}))
.then(() => {
	console.log('Exiting');
	process.exit(0);
})
.catch(err => {
	console.log('here');
	console.error(err);
	process.exit(1);
})
