/*
* <PERSON><PERSON><PERSON> resends ticket emails:
* - payment receipt
* - ticket with QR code
*
* <PERSON>ript params (all required):
* - connection - DB connection string
* - event - Event ID
* - date_start - min date paid date (YYYY-MM-DD)
* - date_end - max date paid date (YYYY-MM-DD)
* - payment_type - payment type (card, ach etc.), default - card
* */

const moment = require('moment');
const argv = require('optimist').argv;
const dbLib = require('../api/lib/db');

require('../scheduler/globals-loder').init();

const DB_CONNECTION = argv.connection;
const EVENT_ID = argv.event;
const DATE_START = __formatDate(argv.date_start);
const DATE_END = __formatDate(argv.date_end);
const PAYMENT_TYPE = __getPaymentType(argv.payment_type);

if(!DB_CONNECTION) {
    throw new Error('DB connection required');
}

if(!EVENT_ID) {
    throw new Error('Event ID required');
}

if(!DATE_START) {
    throw new Error('Date start required');
}

if(!DATE_END) {
    throw new Error('Date end required');
}

try {
    global.Db = new dbLib({ connectionString: DB_CONNECTION });
    dbClient = global.Db;
} catch (err) {
    console.error(err);
    process.exit(1);
}

run()
    .then(() => process.exit(0))
    .catch(err => {
        console.error(err);
        process.exit(1);
    })

async function run () {

    try {
        let purchases = await __getData();

        if(!purchases.length) {
            console.log('No payments found');
            return;
        }

        await __processSend(purchases);

    } finally {
        await Db.end();
    }
}

function __formatDate (date) {
    if(!moment(date, 'YYYY-MM-DD').isValid()) {
        throw new Error('Invalid Date format! Should be YYYY-MM-DD')
    }

    return date;
}

function __getPaymentType (paymentType) {
    if(_.isEmpty(paymentType)) {
        return SWTPaymentsService.CARD_METHOD;
    }

    if(
        ![SWTPaymentsService.CARD_METHOD, SWTPaymentsService.FREE_METHOD, SWTPaymentsService.ACH_METHOD]
        .includes(paymentType)
    ) {
        throw new Error('Payment TYpe Invalid');
    }

    return paymentType;
}

function __getData () {
    let query = `
        SELECT payment.purchase_id,
               (SELECT COALESCE(ARRAY_TO_JSON(ARRAY_AGG(ROW_TO_JSON("t"))), '[]'::JSON)
                FROM (
                         SELECT ticket.ticket_barcode,
                                et.ticket_type,
                                payment.purchase_id payment_id
                         FROM purchase ticket
                                  JOIN purchase_ticket pt ON pt.purchase_id = ticket.purchase_id
                                  JOIN event_ticket et ON et.event_ticket_id = pt.event_ticket_id
                         WHERE ticket.is_ticket = TRUE
                           AND ticket.status = 'paid'
                           AND ticket.type = $4
                           AND ticket.canceled_date IS NULL
                           AND ticket.payment_for = 'tickets'
                           AND ticket.linked_purchase_id = payment.purchase_id
                     ) "t"
               ) "tickets"
        FROM purchase payment
                 JOIN event e ON e.event_id = payment.event_id
                    AND ("tickets_settings" ->> 'require_recipient_name_for_each_ticket')::BOOLEAN IS TRUE
        WHERE payment.type = $4
          AND payment.payment_for = 'tickets'
          AND payment.is_payment IS TRUE
          AND payment.is_ticket IS FALSE
          AND payment.canceled_date IS NULL
          AND payment.date_paid >= $1
          AND payment.date_paid <= $2
          AND payment.event_id = $3;
    `;

    return Db.query(query, [DATE_START, DATE_END, EVENT_ID, PAYMENT_TYPE]).then(result => result?.rows || []);
}

async function __processSend (purchases) {
    for (const purchase of purchases) {
        await Promise.all([
            PAYMENT_TYPE !== SWTPaymentsService.FREE_METHOD
                ? __sendReceipt(purchase.purchase_id)
                : Promise.resolve(),
            __sendTickets(purchase.tickets)
        ]);
    }
}

async function __sendReceipt (paymentID) {
    await SWTReceiptService.sendPaymentInfo(EVENT_ID, paymentID);

    console.log(`Receipt ${paymentID} sent`);
}

async function __sendTickets (tickets) {
    for(const ticket of tickets) {
        await __sendTicket(ticket);
    }
}

async function __sendTicket (ticket) {
    await SWTReceiptService.sendTicketNotification(EVENT_ID, ticket.ticket_barcode, ticket.ticket_type);

    console.log(`Payment ${ticket.payment_id} ticket ${ticket.ticket_barcode} sent.`);
}
