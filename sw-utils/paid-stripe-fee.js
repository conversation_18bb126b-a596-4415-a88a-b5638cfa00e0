/* jshint node:true */
/* jshint esversion:6 */

/**
* This script updates/creates "stripe_charge" rows for not-connected charges
*/

const SW_ACC_SK = '********************************';
const stripe 	= require('stripe')(SW_ACC_SK);
const co 		= require('co');
const pg 		= require('pg');

// Docs: https://nodejs.org/docs/latest/api/process.html#process_process_argv
const DB_CONN 			= process.argv[2];
const YEAR 				= parseInt(process.argv[3], 10); 

if (!DB_CONN) {
	throw new Error('No Connection String passed!');
}

if (!YEAR || (YEAR < 2014)) {
	throw new Error('Invalid year passed!');
}

// Docs: https://github.com/brianc/node-postgres/wiki/Client
const pgClient = new pg.Client(DB_CONN);

return co(function* () {
	yield (clientConnect(pgClient));

	let createdFrom = new Date(YEAR, 0, 1);

	console.log('Finding rows, created after (or eql)', createdFrom);
	createdFrom 	= parseInt(createdFrom.getTime() / 1000, 10);

	let chargesQty 	= yield (getNotConnectedCharges(createdFrom, pgClient));

	yield (clientDisconnect(pgClient));

	console.log('Total:', chargesQty);
})
.catch(err => {
	console.error(err);
	process.exit(1);
})

function getNotConnectedCharges (gte, pgClient, result = 0, starting_after = undefined, limit = 100, iteration = 1) {
    return stripe.charges.list({
        created: { gte },
        limit,
        starting_after,
        include: ['total_count']
    }).then(resp => {
    	if (iteration === 1) {
    		console.log('=== Total', resp.total_count, 'rows to be modified ===');
    	}

    	return resp.data;
    })
    .then(charges => {
        if (charges.length === 0) {
            return result;
        } else {
        	return beginTransaction(pgClient)
        	.then(() => 
        		upsertDefaultStripeChargeRows(charges, pgClient)
        		.then(modifiedRowsQty => 
        			commitTransaction(pgClient)
        			.then(() => modifiedRowsQty)
        		)
        		.catch(err => 
					rollbackTransaction(pgClient)
					.then(() => Promise.reject(err))
				)
        	).then(modifiedRowsQty => {
        		let lastItem 	= charges[charges.length - 1];

        		result += modifiedRowsQty;

        		console.log(
        			`#${iteration}` 	, 
        			'got:' 				, charges.length,
        			'last_created:' 	, new Date(lastItem.created * 1000),
        			'default:' 			, modifiedRowsQty, 
        			'subtotal:' 		, result
        		);

        		

        		return getNotConnectedCharges(gte, pgClient, result, lastItem.id, limit, ++iteration);
        	})
        }
    })
}

function upsertDefaultStripeChargeRows (charges, dbClient) {
	return co(function* () {
		let defaultCharges = charges.filter(ch => (ch.destination === null && ch.status === 'succeeded'));

		if (defaultCharges.length === 0) {
			return 0;
		}

		defaultCharges = yield (expandCharges(defaultCharges));

		let updSQLQuery = genUpdSQLQuery(defaultCharges);

		let updatedCharges =  yield (
			runSQLQuery(dbClient, updSQLQuery)
			.then(res => res.rows.map(charge => charge.stripe_charge_id))
		);

		let rowsToInsert;

		if (updatedCharges.length > 0) {
			rowsToInsert = defaultCharges.filter(charge => {
				return (updatedCharges.indexOf(charge.stripe_charge_id) === -1);
			});
		} else {
			rowsToInsert = defaultCharges;
		}

		let insSQLQuery = getInsSQLQuery(rowsToInsert);

		let modifiedRowsQty = yield (runSQLQuery(dbClient, insSQLQuery).then(res => {
			return updatedCharges.length + res.rowCount;
		}));

		return modifiedRowsQty;
	});
}

function splitArray (arr, chunkSize) {
	if(!Array.isArray(arr)) {
		throw new Error('Expecting first argument to be an array');
	}

	if(arr.length === 0) {
		return [];
	}

	let subArrSize = (Math.abs(chunkSize) || 5);

	let chunksQty = Math.ceil(arr.length / subArrSize);

	let result = [];

	for (let i = 0, start, end; i < chunksQty; ++i) {
		start 	= i * subArrSize;
		end 	= start + subArrSize;

		result.push(arr.slice(start, end));
	}

	return result;
}

function approx (n) {
	return (Math.round(parseFloat(n) * 100) / 100) || 0;
}


function expandCharges (chargesList) {
	return splitArray(chargesList, 20).reduce((prev, sublist) => {
		return prev.then(result => {
			return Promise.all(
				sublist.map(charge => expandChargeObject(charge.id))
			).then(expanded => {
				return (result = result.concat(expanded));
			})
		})
	}, Promise.resolve([]));
}

function expandChargeObject (chargeID) {
	return stripe.charges.retrieve(chargeID, {
	    expand: [
	    	'balance_transaction',
	        'refunds.data.balance_transaction'
	    ]
	}).then(formatChargeObject)
}

function countRefundedFee (refundsList) {
	return refundsList.reduce((sum, refund) => {
        if (refund.status === 'succeeded') {
        	/* we have negative fee for refunds */
            sum += refund.balance_transaction.fee || 0
        }

        return sum;
    }, 0);
}

function formatChargeObject (charge) {
	let amount 				= charge.amount,
		refundedAmount 		= charge.amount_refunded || 0;

	amount = approx((amount - refundedAmount) / 100);

	let stripeProcessingFee 		= charge.balance_transaction.fee,
		stripeProcessingFeeRefunded = countRefundedFee(charge.refunds.data);

	stripeProcessingFee = approx((stripeProcessingFee + stripeProcessingFeeRefunded) / 100);

	return {
		stripe_charge_id 		: charge.id,
		fee 					: stripeProcessingFee,
		balance_transaction 	: charge.balance_transaction,
		collected_fee 			: 0,
		amount,
	}
}
/* === DB UTILS === */

function runSQLQuery (client, query) {
	return new Promise((resolve, reject) => {
		client.query(query, (err, result) => {
			if (err) {
				err.query = query;
				reject(err);
			} else {
				resolve(result);
			}
		})
	});
}

function beginTransaction (client) {
	return runSQLQuery(client, 'BEGIN;');
}

function commitTransaction (client) {
	return runSQLQuery(client, 'COMMIT;');
}

function rollbackTransaction (client) {
	return runSQLQuery(client, 'ROLLBACK;');
}

function clientConnect (client) {
	return new Promise((resolve, reject) => {
		client.connect(err => {
  			if (err) {
  				reject(err);
  			} else {
  				resolve();
  			}
  		})
	})
}

function clientDisconnect (client) {
	return new Promise((resolve, reject) => {
		client.end(err => {
			if (err) {
				reject(err);
			} else {
				resolve();
			}
		})
	});	
}

// Replace single quote for PG
function prepareJSONObject (destination, obj) {
	Object.keys(obj).forEach(key => {
		let val = obj[key];

		if (Object.prototype.toString.call(val) === '[object Object]') {
			destination[key] = prepareJSONObject({}, val);
		} else if (Object.prototype.toString.call(val) === '[object String]') {
			destination[key] = val.replace(/'/g, '\'\'');
		} else {
			destination[key] = val;
		}
	})

	return destination;
}

function prepareJSONForSQL (list) {
	let prepared = [];

	list.forEach(listItem => {

		let preparedItem = prepareJSONObject({}, listItem);

		prepared.push(preparedItem)
	})
	
	return JSON.stringify(prepared);
}

function genUpdSQLQuery (feesList) {

	let feesStringified = prepareJSONForSQL(feesList);

	return (
		`UPDATE "stripe_charge" sch 
		 SET "amount" 				= "d"."amount",
		 	 "fee"					= "d"."fee",
		 	 "balance_transaction" 	= "d"."balance_transaction",
		 	 "collected_fee" 		= "d"."collected_fee" 
		 FROM (
			SELECT 
			 	"d"->>'stripe_charge_id' "stripe_charge_id",
			 	("d"->>'amount')::NUMERIC "amount",
			 	("d"->>'fee')::NUMERIC "fee",
			 	("d"->>'balance_transaction')::JSON "balance_transaction",
			 	("d"->>'collected_fee')::NUMERIC "collected_fee"
			 FROM JSON_ARRAY_ELEMENTS('${feesStringified}') "d"
		 ) "d"
		 WHERE sch."stripe_charge_id" = d."stripe_charge_id"
		 	AND sch."type" = 'default'
		 RETURNING sch."stripe_charge_id"`
	)
}

function getInsSQLQuery (feesListToInsert) {
	let feesStringified = prepareJSONForSQL(feesListToInsert);

	return (
		`INSERT INTO "stripe_charge" (
			"stripe_charge_id", "amount", "fee", "balance_transaction", "collected_fee", "type"
		 )
		 SELECT 
		 	"d"->>'stripe_charge_id' "stripe_charge_id",
		 	("d"->>'amount')::NUMERIC "amount",
		 	("d"->>'fee')::NUMERIC "fee",
		 	("d"->'balance_transaction')::JSON "balance_transaction",
		 	("d"->>'collected_fee')::NUMERIC "collected_fee",
		 	'default'::TEXT "type"
		 FROM JSON_ARRAY_ELEMENTS('${feesStringified}') "d"
		 RETURNING "stripe_charge_id"`
	)
}
