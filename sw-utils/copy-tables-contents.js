'use strict';

/* jshint node:true */
/* jshint esversion:6 */
const pg 			= require('pg');
const sqlGen 		= require('./common/sql-query-gen');
const _             = require('lodash');

const DB_CONN 	 	= process.argv[2];

if (!DB_CONN) {
	console.error(new Error('No Connection String passed!'));
	process.exit(1);
}

const PROD_DB_PSWD 	= process.argv[3];

if (!PROD_DB_PSWD) {
	console.error(new Error('No Password Passed!'));
	process.exit(1);
}

const PROD_DBLINK_CONN
				= `dbname=sw host=sw.cuxax2u4hbuo.us-east-1.rds.amazonaws.com user=sw_website password=${PROD_DB_PSWD}`;

const EVENT_ID   = Number(process.argv[4]);
const TABLE_NAME = isVal(process.argv[5]) ? String(process.argv[5]) : null;

if (!EVENT_ID || (EVENT_ID <= 0)) {
	console.error(new Error('Invalid Event ID!'));
	process.exit(1);
}

const IS_CHILD_PROCESS = (process.env.__IS_CHILD__ === true || process.env.__IS_CHILD__ === 'true')

// Docs: https://github.com/brianc/node-postgres/wiki/Client
const db 		    = new pg.Client({ connectionString: DB_CONN });
const sqlGenerator 	= new sqlGen(PROD_DBLINK_CONN);

const UPSERT 	= 'upsert';
const REPLACE 	= 'replace';

const DEFAULT_CO_EO = '<EMAIL>';

const TABLES = [
	{ name: 'event' 				, action: REPLACE },
    { name: 'event_location' 	    , action: REPLACE },
    { name: 'event_camp' 		    , action: REPLACE },
    { name: 'event_ticket' 		    , action: REPLACE },
	{ name: 'division' 				, action: REPLACE },
	{
	    name: 'roster_team',
        action: REPLACE,
        subTables: [
            {
                name 	: 'master_team',
                action 	: UPSERT,
                where 	: '"master_team_id" IN (SELECT "master_team_id" FROM "roster_team" WHERE "event_id" = ($1)::INTEGER)'
            }
        ]
    },
	{
	    name: 'roster_club',
        action: REPLACE,
        subTables: [{
            name 	: 'master_club',
            action 	: UPSERT,
            where 	: '"master_club_id" IN (SELECT "master_club_id" FROM "roster_club" WHERE "event_id" = ($1)::INTEGER)'
        }]
    },
	{ name: 'courts' 				, action: REPLACE },
	{ name: 'division_standing' 	, action: REPLACE },
	{ name: 'event_journal' 		, action: REPLACE },
    {
        name: 'matches',
        action: REPLACE,
    },
    {
        name: 'poolbrackets',
        action: REPLACE,
    },
	{ name: 'results' 				, action: REPLACE },
    {
        name: 'rounds',
        action: REPLACE,
    },
	{ name: 'app_logs' 				, action: REPLACE },
    {
        name: 'purchase',
        action: UPSERT,
        excludedColumns: ['phone', 'stripe_charge_id', 'stripe_payment_id'],
        subTables: [
            {
                name    : 'purchase_ticket',
                action  : UPSERT,
                where   : '"purchase_id" IN (SELECT "purchase_id" FROM "purchase" WHERE "event_id" = ($1)::INTEGER)'
            },
            {
                name    : 'purchase_team',
                action  : UPSERT,
                where   : '"purchase_id" IN (SELECT "purchase_id" FROM "purchase" WHERE "event_id" = ($1)::INTEGER)'
            },
            {
                name    : 'purchase_history',
                action  : UPSERT,
                where   : '"purchase_id" IN (SELECT "purchase_id" FROM "purchase" WHERE "event_id" = ($1)::INTEGER)'
            }
        ]
    },
    {
        name: 'event_official_schedule',
        action : REPLACE,
        subTables: [
            {
                name    : 'event_official',
                action  : REPLACE,
                excludedColumns: ['arbiterpay_username', 'arbiterpay_account_number'],
                where   : '"event_official_id" IN (SELECT "event_official_id" FROM "event_official" WHERE "event_id" = ($1)::INTEGER)'
            },
            {
                name    : 'event_official_group',
                action  : REPLACE,
                where   : '"event_official_group_id" IN (SELECT "event_official_group_id" FROM "event_official_group" WHERE "event_id" = ($1)::INTEGER)'
            },
            {
                name    : 'official',
                action  : UPSERT,
                excludedColumns: ['emergency_phone', 'arbiter_pay_username', 'arbiter_pay_account_number'],
                where   : '"official_id" IN (SELECT "official_id" FROM "official" WHERE "official_id" = ($1)::INTEGER)'
            }
        ]
    },
].filter(table => TABLE_NAME ? table.name === TABLE_NAME : true);

function __formatTables () {
    return TABLES.reduce((all, table) => {
        all.push(_.omit(table, 'subTables'));

        if(table.subTables && table.subTables.length) {
            all = all.concat(table.subTables);
        }

        return all;
    }, []);
}

function copyTablesContents () {
    let tables = __formatTables();

	return tables.reduce((prev, tableData) => {
		return prev.then(() => {
			console.log('Started', tableData.name);
			return getTableColumns(tableData.name)
			.then(columns => {
				if (tableData.action === UPSERT) {
					return upsertTableContents(columns, tableData);
				} else {
					return replaceTableContents(columns, tableData);
				}
			})
			.then(() => {
				console.log('Finished', tableData.name);
				console.log(' === ');
			})
		})
	}, Promise.resolve())
}


function upsertTableContents (columns, tableData) {
	return Promise.resolve().then(() => {
		let sqlQuery =  sqlGenerator.generateUpsertSQLQuery(columns, tableData, EVENT_ID);
		return db.query(sqlQuery);
	})
}

function replaceTableContents (columns, tableData) {
	return db.query(`DELETE FROM "${tableData.name}" WHERE "event_id" = $1`, [EVENT_ID])
	.then(() => {
		let sqlQuery = sqlGenerator.generateInsertSQLQuery(columns, tableData, EVENT_ID);
		return db.query(sqlQuery);
	})
}

function getTableColumns (tableName) {
	return Promise.resolve().then(() => {
		let query = sqlGenerator.generateTableColumnsSQLQuery(tableName);
		return db.query(query);
	}).then(result => result.rows)
}

function toggleEventJournalTriggerQuery(flag) {
    return `
        UPDATE event
        SET swb_settings = COALESCE("swb_settings"::JSONB, '{}'::JSONB) || '{ "disable_event_journal_trigger": ${flag} }'::JSONB
        WHERE event_id = $1`
}

function addDefaultCoOwnerQuery () {
    return `INSERT INTO "event_user" (
              "user_id", "event_owner_id", "event_id", "role_co_owner"
            )
              SELECT
                u."user_id",
                (SELECT e."event_owner_id"
                 FROM "event" e
                 WHERE e."event_id" = $1) "event_owner_id",
                $1,
                TRUE
              FROM "user" u
              WHERE u."email" = $2 AND NOT EXISTS(
                  SELECT 1
                  FROM "event_user" AS "eu"
                  WHERE eu."user_id" = u."user_id" AND eu."event_id" = $1
              )`;
}

function beforeCopy() {
    return Promise.resolve()
        .then(() => {
            const query = sqlGenerator.checkEventQuery(EVENT_ID);
            return db.query(query).then(result => result.rowCount > 0)
        })
        .then(success => {
            if (success) {
                return db.query(toggleEventJournalTriggerQuery(true), [EVENT_ID]);
            }
            throw new Error(`EventID: [${EVENT_ID}] not found.`)
        })
}

function afterCopy() {
    return Promise.resolve().then(() => {
        return Promise.all([
            db.query(toggleEventJournalTriggerQuery(false), [EVENT_ID]),
            db.query(addDefaultCoOwnerQuery()             , [EVENT_ID, DEFAULT_CO_EO])
        ])
    })
}

function isVal(x) {
    return (x !== undefined && x !== null) && (x !== 'undefined' && x !== 'null');
}

db.connect()
.then(() => {
	return db.query('BEGIN');
})
.then(() => {
    return beforeCopy();
})
.then(() => {
	return copyTablesContents();
})
.then(() => {
    return afterCopy();
})
.then(() => {
	return db.query('COMMIT');
})
.then(() => {
	return db.end();
})
.then(() => {
	process.exit(0);
})
.catch(err => {
	if (IS_CHILD_PROCESS) {
		if (['[object Error]', '[object Object]'].includes(Object.prototype.toString.call(err))) {
			delete err.query;
		}
	}

	console.error(err);
	process.exit(1);
})
