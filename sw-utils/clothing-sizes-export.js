'use strict';
const {argv} = require('optimist');
const xlsx = require('xlsx');
const {Client} = require('pg');
const path = require('path');
global._ = require('lodash');
global.squel = require('../api/services/squel');
const clothingService = require('../api/services/official/_ClothingService');
const {
    parseParameterObject,
    showError,
    fieldMaps,
    formatTable,
    sheet_add_merged_cells,
    sheet_add_new_lines,
    DEFAULT_WRITE_OPTIONS,
} = require('./export-helper');


const $EVENT_ID = parseInt(argv.event, 10);
const $ROLE = argv.role;
const $CONN_STR = argv.connection;
const $OFFICIALS_QUERY = argv.officialsQuery;

$EVENT_ID || showError('Invalid Event Identifier');
$ROLE || showError('Invalid role');
$CONN_STR || showError('Invalid connection string');
$OFFICIALS_QUERY || showError('Invalid officials query');

const fileID = Date.now();

(async () => {
    const connectionString = await parseParameterObject($CONN_STR);
    const officialsQuery = await parseParameterObject($OFFICIALS_QUERY);

    const client = new Client(connectionString);
    await client.connect();
    global.Db = client;
    const { rows: officials } = await client.query(officialsQuery);

    const originEnd = {origin: -1};

    const { officialGroups } = await clothingService.findSizesForOfficials(officials, $EVENT_ID, $ROLE);
    let sheet = {'!ref': 'A0'};
    let firstGroup = true;
    for(const gender in officialGroups) {
        if(!firstGroup) {
            sheet_add_new_lines(sheet);
        }
        const { eventClothes, officials, totals } = officialGroups[gender];
        if(officials.length < 1) {
            continue;
        }
        const width = 3 + eventClothes.length;
        xlsx.utils.sheet_add_aoa(sheet, formatTable(officials, fieldMaps.officialClothingSizes(eventClothes)), originEnd);
        const genderTitle = gender === 'male' ? 'Mens' : 'Womens';
        sheet_add_merged_cells(sheet, genderTitle, { width, height:2 });
        eventClothes.forEach(({common_item_id, title}) => {
            const cellWidth = 6;
            const height = Math.max(1, Math.ceil(totals[common_item_id].length / cellWidth));
            const { r } = sheet_add_merged_cells(sheet, title, { width: 1, height });
            totals[common_item_id].forEach(({size, count}, i) => {
                sheet_add_merged_cells(sheet, `${count}/${size}`, { origin: {r: r + Math.floor(i/cellWidth), c: 1 + i % cellWidth} });
            });
        });
        firstGroup = false;
    }
    const exportDirectoryPath = path.resolve(__dirname, '..', '..', 'export');
    const filePath = path.resolve(exportDirectoryPath, `${$EVENT_ID}_${$ROLE}_clothing_${fileID}.xlsx`);
    const sheetName = 'Clothing Sizes';

    const workbook = xlsx.utils.book_new();
    xlsx.utils.book_append_sheet(workbook, sheet, sheetName);
    xlsx.writeFile(workbook, filePath, DEFAULT_WRITE_OPTIONS);
    process.stdout.write(fileID.toString());
    await client.end();
})().catch(err => {
    if(err instanceof Error) {
        console.error(err);
        process.exit(1);
    }
    showError(err.message || err);
});

