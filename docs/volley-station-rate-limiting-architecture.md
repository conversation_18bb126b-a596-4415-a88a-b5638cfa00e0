# VolleyStation API Rate Limiting Architecture

## Executive Summary

This document outlines the implementation of a comprehensive Redis-based rate limiting solution for the VolleyStation API endpoints. The solution integrates seamlessly with the existing sw-main project architecture, using token-based identification to protect against abuse while maintaining high performance and scalability.

**Key Benefits:**
- **Database Protection:** Prevents excessive load on resource-intensive VolleyStation queries
- **Token-Based Limiting:** Uses authentication tokens (not IP addresses) for precise user-based rate limiting
- **Seamless Integration:** Leverages existing Redis infrastructure and Sails.js policy system
- **Production Ready:** Built on proven libraries with comprehensive error handling and monitoring

## Technical Architecture

### Current System Analysis

**Existing Infrastructure:**
- **Redis Version:** 2.8.0 (via connect-redis dependency)
- **Connection Configuration:** `sails.config.connections.redis`
- **Authentication System:** Token-based using `Authorization` header with `sails.config.volleyStation.apiKey`
- **Policy Chain:** `volleyStationAuth` → `volleyStationEventAccess` → controller actions

**Affected Endpoints:**
- `GET /api/v2/volley-station/:eventId/schedule` (schedule.js)
- `GET /api/v2/volley-station/:eventId/team-roster` (team-roster.js)

### Selected Rate Limiting Library

**Primary Choice: `rate-limiter-flexible`**

**Justification:**
1. **Redis 2.8.0 Compatibility:** Explicitly supports Redis 2.x versions
2. **Token-Based Limiting:** Native support for custom key extraction
3. **Production Proven:** Used by major applications with excellent maintenance record
4. **Sails.js Compatible:** Works seamlessly with Express-style middleware
5. **Advanced Features:** Supports sliding windows, burst protection, and distributed limiting

**Alternative Options Evaluated:**
- **express-rate-limit + rate-limit-redis:** Good but requires additional Redis store package
- **bottleneck:** Excellent for task queuing but overkill for simple API rate limiting

### Implementation Architecture

```
Request Flow:
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐    ┌──────────────┐
│   Client        │───▶│  volleyStationAuth│───▶│ volleyStationRate│───▶│  Controller  │
│   Request       │    │  (Token Auth)    │    │  (Rate Limiting) │    │  Action      │
└─────────────────┘    └──────────────────┘    └─────────────────┘    └──────────────┘
                                ▲                        ▲
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │ VolleyStation    │    │ Redis Store     │
                       │ Config           │    │ (Rate Limits)   │
                       └──────────────────┘    └─────────────────┘
```

### Redis Key Strategy

**Key Pattern:** `rate-limit:volley-station:{token_hash}:{endpoint}`

**Examples:**
- `rate-limit:volley-station:a1b2c3d4:schedule`
- `rate-limit:volley-station:a1b2c3d4:team-roster`

**Benefits:**
- **Namespace Isolation:** Prevents conflicts with existing cache keys
- **Token-Based:** Each API token gets independent rate limits
- **Endpoint-Specific:** Different limits can be applied per endpoint type
- **Efficient Cleanup:** TTL-based automatic expiration

### Rate Limiting Configuration

**Proposed Limits:**
- **Schedule Endpoint:** 100 requests per 15 minutes per token
- **Team Roster Endpoint:** 100 requests per 15 minutes per token
- **Burst Protection:** Maximum 10 requests per minute for initial burst

**Rationale:**
- **Database Protection:** Limits align with cache TTL (5 minutes) to maximize cache hit ratio
- **Reasonable Usage:** Allows legitimate polling while preventing abuse
- **Burst Tolerance:** Accommodates legitimate batch operations

## Implementation Plan

### Phase 1: Service Creation (1-2 hours)

1. **Install Dependencies**
   ```bash
   npm install rate-limiter-flexible
   ```

2. **Create Rate Limiting Service**
   - File: `api/services/VolleyStationRateLimit.js`
   - Integrate with existing Redis connection
   - Implement token-based key generation
   - Configure endpoint-specific limits

3. **Create Rate Limiting Policy**
   - File: `api/policies/volley-station/rateLimit.js`
   - Extract token from Authorization header
   - Apply rate limiting before controller execution
   - Handle rate limit violations gracefully

### Phase 2: Integration (1 hour)

1. **Update Policy Configuration**
   - Modify `config/policies.js`
   - Add rate limiting to VolleyStation endpoint chain
   - Ensure proper policy ordering

2. **Error Response Standardization**
   - Implement consistent 429 (Too Many Requests) responses
   - Include retry-after headers
   - Provide clear error messages

### Phase 3: Testing & Monitoring (2-3 hours)

1. **Unit Tests**
   - Test rate limiting logic
   - Verify token extraction
   - Validate Redis key generation

2. **Integration Tests**
   - Test full request flow
   - Verify policy chain execution
   - Test rate limit enforcement

3. **Load Testing**
   - Simulate high request volumes
   - Verify Redis performance
   - Test rate limit accuracy

## Configuration Options

### Environment Variables

```javascript
// config/volleyStation.js
module.exports.volleyStation = {
    apiKey: process.env.VOLLEY_STATION_API_KEY,
    rateLimit: {
        schedule: {
            points: 100,        // Number of requests
            duration: 900,      // Per 15 minutes (900 seconds)
            blockDuration: 900, // Block for 15 minutes when exceeded
        },
        teamRoster: {
            points: 100,
            duration: 900,
            blockDuration: 900,
        },
        burst: {
            points: 10,         // Burst protection
            duration: 60,       // Per minute
            blockDuration: 60,  // Block for 1 minute
        }
    }
};
```

### Redis Configuration

**Connection Reuse:**
- Uses existing `sails.config.connections.redis`
- No additional Redis connections required
- Leverages current connection pooling

**Key Expiration:**
- Automatic TTL based on rate limit duration
- No manual cleanup required
- Memory efficient

## Error Handling

### Rate Limit Violation Response

```json
{
    "error": "Rate limit exceeded",
    "message": "Too many requests for this API token",
    "retryAfter": 300,
    "limit": {
        "requests": 100,
        "window": "15 minutes",
        "remaining": 0,
        "resetTime": "2024-01-15T10:30:00Z"
    }
}
```

### Fallback Behavior

1. **Redis Connection Failure:** Allow requests through (fail-open)
2. **Rate Limiter Error:** Log error and allow request
3. **Invalid Token:** Return 401 (handled by existing auth policy)

## Monitoring and Observability

### Metrics Collection

**Key Metrics:**
- Rate limit hits per endpoint
- Token usage patterns
- Redis operation latency
- Error rates

**Implementation:**
- Integrate with existing logging system
- Use structured logging for metrics
- Monitor Redis memory usage

### Alerting

**Critical Alerts:**
- High rate limit violation rates (potential abuse)
- Redis connection failures
- Unusual token usage patterns

**Warning Alerts:**
- Approaching rate limits for legitimate tokens
- Redis memory usage growth
- Slow Redis operations

## Testing Strategy

### Unit Testing

```javascript
// Test rate limiting service
describe('VolleyStationRateLimit', () => {
    it('should generate correct Redis keys');
    it('should enforce rate limits per token');
    it('should handle Redis errors gracefully');
});

// Test rate limiting policy
describe('volleyStationRateLimit policy', () => {
    it('should extract token from Authorization header');
    it('should apply rate limits before controller');
    it('should return 429 when limit exceeded');
});
```

### Integration Testing

```javascript
// Test full request flow
describe('VolleyStation Rate Limiting Integration', () => {
    it('should allow requests within limits');
    it('should block requests exceeding limits');
    it('should reset limits after time window');
    it('should handle multiple tokens independently');
});
```

### Load Testing

**Scenarios:**
1. **Normal Usage:** Verify performance under typical load
2. **Burst Traffic:** Test burst protection mechanisms
3. **Sustained Load:** Verify rate limiting accuracy over time
4. **Multiple Tokens:** Test concurrent token usage

## Deployment Considerations

### Production Configuration

**Redis Settings:**
- Ensure sufficient memory for rate limiting data
- Monitor Redis memory usage patterns
- Configure appropriate Redis persistence settings

**Rate Limit Tuning:**
- Start with conservative limits
- Monitor usage patterns
- Adjust limits based on actual usage data

### Rollback Plan

**Immediate Rollback:**
1. Remove rate limiting policy from `config/policies.js`
2. Restart application
3. Rate limiting disabled, normal operation restored

**Gradual Rollback:**
1. Increase rate limits to very high values
2. Monitor for issues
3. Complete removal if necessary

### Security Considerations

**Token Security:**
- Rate limiting keys use hashed tokens (not plain text)
- No sensitive data stored in Redis
- Automatic key expiration prevents data accumulation

**DDoS Protection:**
- Rate limiting provides first line of defense
- Complements existing infrastructure protection
- Prevents application-level resource exhaustion

## Integration Requirements

### Existing Policy Integration

**Current Policy Chain:**
```javascript
// config/policies.js
'v2/API/volley-station/*': [volleyStationAuth, volleyStationEventAccess]
```

**Updated Policy Chain:**
```javascript
// config/policies.js
'v2/API/volley-station/*': [volleyStationAuth, volleyStationRateLimit, volleyStationEventAccess]
```

**Integration Points:**

1. **Token Extraction Consistency**
   - Rate limiting policy must use same token extraction as `volleyStationAuth`
   - Token format: `Authorization: {apiKey}` header
   - Validation: Must match `sails.config.volleyStation.apiKey`

2. **Policy Ordering Requirements**
   - **First:** `volleyStationAuth` - Validates token authenticity
   - **Second:** `volleyStationRateLimit` - Applies rate limiting to valid tokens
   - **Third:** `volleyStationEventAccess` - Validates event access permissions

3. **Error Response Consistency**
   - Rate limiting errors (429) must follow same format as auth errors (401)
   - Maintain consistent JSON response structure
   - Preserve existing error handling patterns

### Redis Configuration Integration

**Connection Reuse Strategy:**
```javascript
// api/services/VolleyStationRateLimit.js
const redis = require('redis');

class VolleyStationRateLimit {
    constructor() {
        // Reuse existing Redis connection configuration
        this._client = redis.createClient(sails.config.connections.redis);
    }
}
```

**Key Namespace Isolation:**
- **Cache Keys:** `cached:dev:*` (existing)
- **Session Keys:** `sess-dev:*` (existing)
- **Rate Limit Keys:** `rate-limit:volley-station:*` (new)

**Redis Database Usage:**
- **Development:** Database 1 (same as sessions)
- **Production:** Database 0 (same as sessions)
- **No additional database required**

### Authentication System Integration

**Token Hashing Strategy:**
```javascript
// Generate consistent hash for rate limiting keys
const crypto = require('crypto');

function generateTokenHash(token) {
    return crypto
        .createHash('sha256')
        .update(token)
        .digest('hex')
        .substring(0, 8); // First 8 characters for brevity
}
```

**Token Validation Flow:**
1. **Extract:** Get token from `Authorization` header
2. **Validate:** Verify against `sails.config.volleyStation.apiKey`
3. **Hash:** Generate consistent hash for Redis key
4. **Rate Limit:** Check/update rate limit counters
5. **Proceed:** Continue to event access validation

### Controller Integration

**No Controller Changes Required:**
- Rate limiting is transparent to controllers
- Controllers continue to receive validated requests
- No additional code required in controller actions

**Request Object Enhancement:**
```javascript
// Rate limiting policy adds metadata to request
req.rateLimit = {
    limit: 100,
    remaining: 95,
    resetTime: new Date('2024-01-15T10:30:00Z'),
    retryAfter: null // Only set when limit exceeded
};
```

### Configuration File Integration

**Update Required Files:**

1. **config/policies.js**
   ```javascript
   const volleyStationRateLimit = require('../api/policies/volley-station/rateLimit');

   module.exports.policies = {
       // ... existing policies
       'v2/API/volley-station/*': [
           volleyStationAuth,
           volleyStationRateLimit,
           volleyStationEventAccess
       ],
   };
   ```

2. **config/volleyStation.js**
   ```javascript
   module.exports.volleyStation = {
       apiKey: 'vs_dev_2eec4d4f84c8fad36784c3ceb5f2ef6113745fe838356675ba4a3f6837a74dea',
       rateLimit: {
           schedule: {
               points: 100,
               duration: 900,
               blockDuration: 900,
           },
           teamRoster: {
               points: 100,
               duration: 900,
               blockDuration: 900,
           },
           burst: {
               points: 10,
               duration: 60,
               blockDuration: 60,
           }
       }
   };
   ```

### Backward Compatibility

**Guaranteed Compatibility:**
- No breaking changes to existing API responses
- No changes to authentication flow
- No modifications to controller interfaces
- No impact on existing caching behavior

**Graceful Degradation:**
- If Redis is unavailable, requests proceed normally
- If rate limiter fails, requests are allowed through
- Existing error handling remains unchanged

---

## Next Steps

1. **Team Review:** Review and approve this architecture document
2. **Implementation:** Execute the implementation plan in phases
3. **Testing:** Comprehensive testing before production deployment
4. **Monitoring:** Establish monitoring and alerting
5. **Documentation:** Update API documentation with rate limiting information

**Estimated Total Implementation Time:** 4-6 hours
**Risk Level:** Low (leverages existing infrastructure)
**Dependencies:** None (uses existing Redis and authentication systems)
