'use strict';

const or                    = require('../api/lib/or');
const isEventOwner = require('../api/policies/isEventOwner');
const isGodMode    = require('../api/policies/isGodMode');
const doesProfileCompleted  = require('../api/policies/doesProfileCompleted');
const UserService           = require('../api/services/UserService');
const eventAccess           = require('../api/policies/eventAccess');
const isHeadOfficial        = require('../api/policies/isHeadOfficial');
const Permissions           = require('../api/services/event/operations');
const isEventSalesManager   = require('../api/policies/isEventSalesManager');
const isSalesManager        = require('../api/policies/isSalesManager');
const exhibitorPurchaseAccess       = require('../api/policies/exhibitorPurchaseAccess');
const salesManagerPurchaseAccess    = require('../api/policies/salesManagerPurchaseAccess');
const housingAPIAuth           = require('../api/policies/housing/auth');
const thsAPIAuth = require('../api/policies/ths/auth');
const eventConnectAPIAuth = require('../api/policies/eventconnect/auth');
const acsAPIAuth           = require('../api/policies/acs/auth');
const ballerTvAuth = require('../api/policies/baller-tv/auth');
const volleyStationAuth = require('../api/policies/volley-station/auth');
const volleyStationRateLimit = require('../api/policies/volley-station/rateLimit');
const volleyStationEventAccess = require('../api/policies/volley-station/eventAccess');

module.exports.policies = {
    '*': true,
    SharedController: {
        '*': ['isAuthenticated'],
    },
    MasterStaffRoleController: {
        '*': ['isAuthenticated', 'isClubDirector']
    },
    MasterStaffController: {
        '*': ['isAuthenticated', 'isClubDirector']
    },
    StripeController: {
        '*'                         : ['isAuthenticated'],
        'webhookUpd'                : ['rawBodyBuffer'], // this action is used by stripe
        'webhook'                   : ['rawBodyBuffer'],
        'handleExpressAccConnection': true,
    },
    'API/Tickets/PaymentController': {
        'getUserPurchases'  : ['isAuthenticated'],
        'buyTickets'        : 'swt/duplicatePaymentCheck',
        'buyTicketsInKiosk' : 'swt/SWTAPIHistory',
    },
    'API/Tickets/SWTApiController': {
        'getScanHistoryCsv': [],
        '*'                : 'swt/SWTAPIHistory',
    },
    'API/Tickets/EventController': {
        'kioskTickets':            'swt/SWTAPIHistory'
    },
    'Club/WebpointController': {
        '*': ['isAuthenticated', 'isClubDirector', 'club/hasMasterClub']
    },
    'Club/MasterTeamController': {
        '*': ['isAuthenticated'],
        'count': ['isAuthenticated', 'isClubDirector'],
        'assignList': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'bulkCreate': ['isAuthenticated', 'isClubDirector', 'club/hasMasterClub'],
        'teamsList': ['isAuthenticated', 'isClubDirector', 'club/hasMasterClub']
    },
    'Club/RosterTeamController': {
        '*': ['isAuthenticated', 'isClubDirector'],
        'enterEvent': [
            'isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink', 'club/isAssignAvailable',
            doesProfileCompleted(UserService.reg.ROLE.CD)
        ],
        'updateEntry': [
            'isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink',
            'club/isAssignAvailable', doesProfileCompleted(UserService.reg.ROLE.CD)
        ],
        'getMembersList': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'assignedTeamsList': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'validateJerseys': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'validateRoster': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'validateTeamsRosters': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink']
    },
    'Club/DivisionsController': {
        '*': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink']
    },
    'Club/OnlineCheckinController': {
        '*': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink', 'validateEventId'],
        'applyCheckin': [
            'isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink',
            doesProfileCompleted(UserService.reg.ROLE.CD)
        ],
        'staffers': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'teamsList': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'showBarcodeDescription': ['validateEventId'],
        'showPrimaryStaffBarcodeDescription': [],
        'resendPrimaryStaffEmail': ['isAuthenticated', or([isEventOwner, isGodMode])]
    },
    'Club/RosterStaffController': {
        '*': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'findAssignedEvents': ['isAuthenticated', 'isClubDirector']
    },
    'Club/RosterAthleteController': {
        '*': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'getAssignedEvents': ['isAuthenticated', 'isClubDirector']
    },
    'Club/ClubController': {
        'find'                  : ['isAuthenticated', 'findClubDirectorData'],
        'foreignRosterImport'   : ['isAuthenticated', 'isClubDirector', 'club/isClubCreated'],
        'update'                : ['isAuthenticated', 'isClubDirector', 'club/isClubCreated'],
        'import'                : ['isAuthenticated', 'isClubDirector', 'club/isClubCreated'],
        'wp_data'               : ['isAuthenticated', 'isClubDirector', 'club/isClubCreated'],
        'create'                : ['isAuthenticated', 'isClubDirector'],
        'sportEngineImportData' : ['isAuthenticated', 'isClubDirector', 'club/isClubCreated'],
        'aauImportData'         : ['isAuthenticated', 'isClubDirector', 'club/isClubCreated'],
    },
    'Official/ProfileController': {
        '*': ['isAuthenticated'],
        'create': ['isAuthenticated', 'hasRoleOfficial'],
        'find': ['isAuthenticated', 'isOfficial'],
        'update': ['isAuthenticated', 'isOfficial']
    },
    'Official/OfficialsController': {
        '*': ['isAuthenticated', 'isOfficial', 'validateEventId', 'isHeadOfficial'],
        'updateAdditionalRestrictions': ['isAuthenticated', or([isEventOwner, isHeadOfficial, isGodMode])],
    },
    'Official/EventController': {
        '*'                     : ['isAuthenticated', 'isOfficial'],
        'show_checked_data'     : ['isAuthenticated', 'isOfficial', 'validateEventId'],
        'assignments'           : ['isAuthenticated', 'isOfficial', 'validateEventId'],
        'check_out'             : ['isAuthenticated', 'isOfficial', 'validateEventId'],
        'update'                : ['isAuthenticated', 'isOfficial', doesProfileCompleted(UserService.reg.ROLE.OFFICIAL)],
        'check_in'              : ['isAuthenticated', 'isOfficial', doesProfileCompleted(UserService.reg.ROLE.OFFICIAL)],
        'setSanctioningCheckFieldOk'    : ['isAuthenticated', or([
            eventAccess([Permissions.STAFF_TAB, Permissions.OFFICIALS_TAB]),
            isHeadOfficial
        ])],
        'sendEntryQRCodes': [
            'isAuthenticated',
            or([
                eventAccess(Permissions.OFFICIALS_TAB),
                isHeadOfficial,
            ]),
        ],
        'showEntryQRCode': ['isAuthenticated', or([isEventOwner, isHeadOfficial, isGodMode])],
    },
    COMEventsController: {
        '*'         : ['isAuthenticated', 'isClubDirector'],
        'find'      : ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink', 'clubAccessByRegion'],
        'get_info'  : ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink']
    },
    MasterAthleteController: {
        '*': ['isAuthenticated', 'isClubDirector']
    },
    'Club/MasterAthleteController': {
        '*': ['isAuthenticated', 'isClubDirector']
    },
    UserController: {
        find: 'isAuthenticated',
        update: 'isAuthenticated'
    },
    UploadController: {
        '*': ['isAuthenticated'],
        teamsImport: ['isAuthenticated', 'isClubDirector']
    },
    GeoController: {
        '*': true
    },
    'SalesManager/SponsorController': {
        '*': ['isAuthenticated', 'isSalesManager'],
    },
    'SalesManager/PaymentController': {
        'pay': ['isAuthenticated', or([
            eventAccess([Permissions.EXHIBITORS_TAB]),
            isSalesManager
        ])],
        'receive': ['isAuthenticated', or([
            eventAccess([Permissions.EXHIBITORS_TAB]),
            isSalesManager
        ])],
        'void': ['isAuthenticated', or([
            eventAccess([Permissions.EXHIBITORS_TAB]),
            isSalesManager
        ])],
        'refund': ['isAuthenticated', or([
            eventAccess([Permissions.EXHIBITORS_TAB]),
            isSalesManager
        ])],
        'changePaymentType': ['isAuthenticated', or([
            eventAccess([Permissions.EXHIBITORS_TAB]),
            isSalesManager
        ])],
        'updateInvoice': ['isAuthenticated', or([
            eventAccess([Permissions.EXHIBITORS_TAB]),
            isSalesManager
        ])],
        'get_payments': ['isAuthenticated', 'isSalesPerson'],
        'get_booths_payments': ['isAuthenticated', 'isSalesPerson'],
    },
    'SalesManager/InvoiceController': {
        '*': ['isAuthenticated', 'isSalesPerson', 'invoiceAccess']
    },
    'SalesManager/EventController': {
        '*': ['isAuthenticated', 'isSalesManager'],
        'index': ['isAuthenticated', 'isSalesPerson'],
        'get_event': ['isAuthenticated', 'isSalesPerson', 'isEventSalesManager'],
        'published_events': ['isAuthenticated', 'isSalesPerson']
    },
    'SalesManager/TicketController': {
        '*': ['isAuthenticated', 'isSalesManager'],
    },
    'SalesManager/EventBoothController': {
        'create': ['isAuthenticated', 'isSalesManager']
    },
    'Club/PaymentController': {
        'save'                  : [
            'isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink',
            doesProfileCompleted(UserService.reg.ROLE.CD)
        ],
        'cancel'                : ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'clubPayments'          : ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'getTeamsPaymentData'   : [
            'isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink',
            doesProfileCompleted(UserService.reg.ROLE.CD)
        ],
        'getPayment'            : ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink'],
        'changePaymentType'     : ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink']
    },
    'Club/CheckInRosterController': {
        '*': ['isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink', 'canAccessEventDataCD', 'CheckInAccessCD']
    },
    'Sponsor/ProfileController' : {
        '*': ['isAuthenticated']
    },

    'Admin/ClubMembersController': {
        '*': ['isAuthenticated'],
        'updateAthleteHeight': []
    },

    'Admin/MonitoringController': {
        '*': ['isAuthenticated']
    },

    'Admin/UserController': {
        'activateEORole': ['isAuthenticated', 'isInfoSW']
    },

    'Admin/StatisticsController': {
        'getFinancialData': ['isAuthenticated'],
        'getFinancialExport': ['isAuthenticated']
    },
    'Admin/SportsEngine/EligibilityController': {
        '*': ['isAuthenticated'],
    },
    'Club/InvoiceController': {
        'renderInvoice': ['isAuthenticated', 'clubInvoiceAccess']
    },
    'Club/EventController': {
        '*': ['isAuthenticated', 'isClubDirector']
    },
    'API/Officials/OfficialsScheduleController': {
        '*': [
            'isAuthenticated',
            or([
                require('../api/policies/OfficialsAPI/OfficialsScheduleAccess'),
                isGodMode,
            ]),
        ],
        'courts': [
            'isAuthenticated',
            or([
                require('../api/policies/OfficialsAPI/OfficialsScheduleAccess'),
                isGodMode,
            ]),
            'validateEventId',
        ],
    },
    'API/Officials/ResultsAppController': {
        '*': ['isAPIAuthenticated', 'OfficialsAPI/checkSessionEventID'],
        'login': [],
        'activeEventsList': []
    },
    'Public/EventController': {
        'index': ['isAuthenticated', 'housing/isHousingManager']
    },
    'Public/TeamsController': {
        'index': ['isAuthenticated', 'housing/isHousingManager'],
        'export_team_list': ['isAuthenticated', 'housing/isHousingManager'],
        'get_notes': ['isAuthenticated', or([require('../api/policies/housing/isHousingManager'), isEventOwner, isGodMode])],
        'add_note': ['isAuthenticated', or([require('../api/policies/housing/isHousingManager'), isEventOwner, isGodMode])],
        'update_note': ['isAuthenticated', or([require('../api/policies/housing/isHousingManager'), isEventOwner, isGodMode])],
        'ths_bookings': ['isAuthenticated', 'housing/isHousingManager', 'housing/isTHSManager'],
        'ths_history': ['isAuthenticated', 'housing/isHousingManager', 'housing/isTHSManager'],
        'club_contacts': ['isAuthenticated', 'housing/isHousingManager']
    },
    'Public/HousingStatusController': {
        'updateTeamStatus': ['isAuthenticated', 'housing/isHousingManager', 'housing/housingManagerEventAccess'],
    },
    'Public/HousingCompanyController': {
        '*': ['isAuthenticated'],
    },
    'API/OnlineCheckin/CheckinScanController': {
        // 'events'    : [],
        'scan'      : ['validateEventId'],
        'checkin'   : ['validateEventId']
    },
    'Common/OfficialInfoLinkController': {
        '*': ['isAuthenticated']
    },
    'Supervisor/EventController': {
        '*': ['isAuthenticated', 'isGodMode']
    },
    /* === AEM === */
    'AEM/AdminController': {
        '*': ['isAuthenticated', 'isGodMode']
    },
    'AEM/GeneralController': {
        '*': ['isAuthenticated'],
        'massSending' : ['isAuthenticated', 'eventsEmailAccess']
    },

    /* === Usav Admin === */

    'USAVAdminController' : {
        '*' : ['isAuthenticated', or([require('../api/policies/isGodMode'),
                                      require('../api/policies/isUsavAdmin')])]
    },


    'FAQController' : {
        '*' : true
    },
    'SignupController': {
        'signup': ['recaptchaValidation']
    },
    'Sponsor/EventController': {
        '*': ['isAuthenticated'],
    },
    'Sponsor/ReceiptController': {
        'getReceiptInfo': ['isAuthenticated', 'isSalesPerson'],
        'updateReceipt': ['isAuthenticated', 'isSalesPerson'],
        'createReceipt': ['isAuthenticated', 'isSalesPerson'],
    },
    'EventExhibitorController': {
        '*': ['isAuthenticated'],
        'getExhibitors': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.EXHIBITORS_TAB]),
                isEventSalesManager
            ])
        ],
        'getExhibitorsList': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.EXHIBITORS_TAB]),
                isEventSalesManager
            ])
        ]
    },
    'EventExhibitorInvoiceController': {
        'getExhibitorInvoices': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.EXHIBITORS_TAB]),
                isEventSalesManager
            ])
        ],
        'getExhibitorInvoiceInitData': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.EXHIBITORS_TAB]),
                isEventSalesManager
            ])
        ],
        'getExhibitorInvoiceInfo': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.EXHIBITORS_TAB]),
                isEventSalesManager
            ])
        ],
        'updateExhibitorInvoice': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.EXHIBITORS_TAB]),
                isEventSalesManager
            ])
        ],
        'deleteExhibitorInvoice': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.EXHIBITORS_TAB]),
                isEventSalesManager
            ])
        ],
        'createExhibitorInvoice': [
            'isAuthenticated',
            or([
                eventAccess([Permissions.EXHIBITORS_TAB]),
                isEventOwner,
                isEventSalesManager
            ])
        ],
    },
    'Sponsor/InvoiceController': {
        '*': [
            'isAuthenticated',
            or([
                exhibitorPurchaseAccess,
                isEventSalesManager,
                eventAccess([Permissions.EXHIBITORS_TAB])
            ]),
        ]
    },
    'PaymentCardController': {
        '*': ['isAuthenticated',
            or([
                require('../api/policies/isGodMode'),
                require('../api/policies/event/isEO'),
                eventAccess([Permissions.EDIT_EVENT])
            ])]
    },
    'Club/MembersPersonalInfoController': {
        '*': ['isAuthenticated', 'isClubDirector', 'club/isClubCreated'],
    },
    'API/EventCheckin/EventCheckinOfficialController': {
        '*': true
    },
    'TicketBuyEntryCodeController': {
        '*': true,
        'upsertSettings': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],
        'couponsList': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],
        'couponTicketsList': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],
        'createCustomCode': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)]
    },
    'Club/PaymentV2Controller': {
        '*': [
            'isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink',
            doesProfileCompleted(UserService.reg.ROLE.CD)
        ]
    },

    /* === New Housing Policy === */
    'v2/API/housing/*': housingAPIAuth,

    'v2/club/bulk-registration/*': [
        'isAuthenticated', 'isClubDirector', 'club/isClubCreated', doesProfileCompleted(UserService.reg.ROLE.CD)
    ],

    'v2/club/purchase/*': [
        'isAuthenticated', 'isClubDirector', 'clubAccessByPrivateLink', doesProfileCompleted(UserService.reg.ROLE.CD)
    ],

    'v2/admission/ticket/*': [
        'isAuthenticated', 'isTicketHolder'
    ],
    'v2/admission/events/*': ['isAuthenticated'],
    'v2/API/swt-app/user/signin': {
        '*': true
    },
    'v2/API/swt-app/wallet/*': ['isAuthenticated'],
    'v2/API/swt-app/tickets': ['isAuthenticated'],
    'v2/API/swt-app/events/*': ['isAuthenticated'],
    'v2/API/swt-app/recognition/*': ['isAuthenticated'],

    'v2/API/email-editor-image/*': ['isAuthenticated'],

    'v2/API/acs/*': acsAPIAuth,

    'v2/user/*': ['isAuthenticated'],
    'v2/admin/*': ['isAuthenticated'],
    'v2/Admin/AAU/*': [],

    'v2/event/reports/*': ['isAuthenticated', eventAccess(Permissions.TICKETS_TAB)],

    'v2/common/refund-policy': {
        '*': true
    },

    'v2/event/custom-form/*': {
        '*': true
    },
    'v2/event/custom-form/export-results': ['isAuthenticated', isEventOwner],

    'v2/stripe/*': ['isAuthenticated'],

    'v2/event/custom-form/builder/*': [
        'isAuthenticated',
        isEventOwner,
        eventAccess(Permissions.CUSTOM_FORMS)
    ],

    'v2/event/tickets-app-user-verification/*': [
        'isAuthenticated',
        isEventOwner,
        eventAccess(Permissions.TICKET_APPLICATION_APPROVE_TAB)
    ],

    'v2/vertical-insurance/webhook': ['rawBodyBuffer'],

    'v2/club/club-invoice/list': ['isAuthenticated', 'isClubDirector', 'club/hasMasterClub'],

    'v2/club/club-invoice/init-payment': ['isAuthenticated', 'isClubDirector', 'club/hasMasterClub'],

    'v2/club/club-invoice/update-payment': ['isAuthenticated', 'isClubDirector', 'club/hasMasterClub'],

    'v2/vertical-insurance/quote/team-registration/link': ['isAuthenticated', 'isClubDirector', 'club/hasMasterClub'],

    'THS/V1Controller': {
        'clubs': thsAPIAuth,
        'teams': thsAPIAuth,
        'reservations': thsAPIAuth,
        'changes': thsAPIAuth,
        'bookings': thsAPIAuth,
    },

    'v2/tilled/accounts': ['isAuthenticated'],

    'v2/API/baller-tv/events': [ballerTvAuth],

    'v2/API/volley-station/*': [volleyStationAuth, volleyStationRateLimit, volleyStationEventAccess],

    'v2/API/notifications/*': ['isAuthenticated'],

    'v2/API/eventconnect/*': eventConnectAPIAuth,
};
