
const {
    CLUB_TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM,
    CLUB_TEAM_ROSTER_STAFF_REMOVED_BY_SYSTEM,
    CLUB_STAFF_SANCTIONING_USAV_REMOVED_BY_SYSTEM,
    CLUB_ATHLETE_SANCTIONING_USAV_REMOVED_BY_SYSTEM,
    CLUB_ATHLETE_DELETED_BY_SYSTEM,
    CLUB_STAFF_DELETED_BY_SYSTEM,
    TEAM_ROSTER_STAFF_REMOVED_BY_SYSTEM,
    TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM,
} = require('../../../api/constants/notification-actions');

class HistoryActions {
    saveAthleteRemovedFromClubRosterAction(tr, memberData) {
        return this.#saveClubHistoryAction(
            tr,
            this.#prepareClubMemberData(memberData),
            CLUB_TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM,
        );
    }

    saveStaffRemovedFromClubRosterAction(tr, memberData) {
        return this.#saveClubHistoryAction(
            tr,
            this.#prepareClubMemberData(memberData),
            CLUB_TEAM_ROSTER_STAFF_REMOVED_BY_SYSTEM,
        );
    }

    saveStaffUSAVDataRemovedAction(tr, memberData) {
        return this.#saveClubHistoryAction(
            tr,
            this.#prepareClubMemberData(memberData),
            CLUB_STAFF_SANCTIONING_USAV_REMOVED_BY_SYSTEM,
        );
    }

    saveAthleteUSAVDataRemovedAction(tr, memberData) {
        return this.#saveClubHistoryAction(
            tr,
            this.#prepareClubMemberData(memberData),
            CLUB_ATHLETE_SANCTIONING_USAV_REMOVED_BY_SYSTEM,
        );
    }

    saveAthleteDeletedAction(tr, memberData) {
        return this.#saveClubHistoryAction(
            tr,
            this.#prepareClubMemberData(memberData),
            CLUB_ATHLETE_DELETED_BY_SYSTEM,
        );
    }

    saveStaffDeletedAction(tr, memberData) {
        return this.#saveClubHistoryAction(
            tr,
            this.#prepareClubMemberData(memberData),
            CLUB_STAFF_DELETED_BY_SYSTEM,
        );
    }

    saveStaffRemovedFromEventRosterAction(tr, memberData) {
        return this.#saveEventHistoryAction(
            tr,
            this.#prepareEventMemberData(memberData),
            TEAM_ROSTER_STAFF_REMOVED_BY_SYSTEM,
        );
    }

    saveAthleteRemovedFromEventRosterAction(tr, memberData) {
        return this.#saveEventHistoryAction(
            tr,
            this.#prepareEventMemberData(memberData),
            TEAM_ROSTER_ATHLETE_REMOVED_BY_SYSTEM,
        );
    }

    async #saveClubHistoryAction(tr, member, action, notes) {
        if(!tr) {
            throw new Error('Transaction should be defined');
        }

        if(!member?.master_club_id) {
            throw new Error('Member should have master_club_id');
        }

        if(!member?.master_athlete_id && !member?.master_staff_id) {
            throw new Error('Member should have master_athlete_id or master_staff_id');
        }

        if(!action) {
            throw new Error('Action should be defined');
        }

        const query = knex('club_history')
            .insert({
                master_club_id: member.master_club_id,
                master_athlete_id: member?.master_athlete_id || null,
                master_staff_id: member?.master_staff_id || null,
                master_team_id: member?.master_team_id || null,
                action,
                notes: notes || null,
            });

        return tr.query(query);
    }


    #prepareClubMemberData(memberData) {
        return {
            master_club_id: memberData?.master_club_id,
            master_athlete_id: memberData?.master_athlete_id,
            master_staff_id: memberData?.master_staff_id,
            master_team_id: memberData?.master_team_id,
        }
    }

    async #saveEventHistoryAction(tr, member, action, notes) {
        if(!tr) {
            throw new Error('Transaction should be defined');
        }

        if(!action) {
            throw new Error('Action should be defined');
        }

        if(!member?.roster_team_id) {
            throw new Error('Member should have roster_team_id');
        }

        if(!member?.roster_club_id) {
            throw new Error('Member should have roster_club_id');
        }

        if(!member?.event_id) {
            throw new Error('Member should have event_id');
        }

        const query = knex('event_change')
            .insert({
                event_id: member.event_id,
                roster_team_id: member.roster_team_id,
                master_staff_id: member.master_staff_id || null,
                action,
                comments: notes || null,
            });

        return tr.query(query);
    }

    #prepareEventMemberData(memberData) {
        return {
            event_id: memberData?.event_id,
            roster_team_id: memberData?.roster_team_id,
            master_staff_id: memberData?.master_staff_id,
            roster_club_id: memberData?.roster_club_id,
        };
    }

}

module.exports = new HistoryActions();
