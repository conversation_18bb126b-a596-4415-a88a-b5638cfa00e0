

module.exports = {
    /**
     *
     * @api {post} /api/signup User Signup
     * @apiDescription User signup
     * @apiGroup User
     *
     */
    'post /api/signup': 'SignupController.signup',

    /**
     *
     * @api {post} /api/signout User Signout
     * @apiDescription User signout
     * @apiGroup User
     *
     */
    'post /api/signout': 'SigninController.signout',

    /**
     *
     * @api {get} /api/get-signin User Signin [GET]
     * @apiDescription User signin (allow GET request to sign-in)
     * @apiGroup User
     *
     */
    'get /api/get-signin': 'SigninController.signin', // Allow GET request to sign-in

    /**
     *
     * @api {post} /api/get-signin User Signin
     * @apiDescription User signin
     * @apiGroup User
     *
     */
    'post /api/signin': 'SigninController.signin',

    /**
     *
     * @api {get} /api/facebook User Facebook Signin
     * @apiDescription User signin with facebook
     * @apiGroup User
     *
     */
    'get /signin/facebook': 'SigninController.facebook',

    /**
     *
     * @api {get} /api/twitter User Twitter Signin
     * @apiDescription User signin with twitter
     * @apiGroup User
     *
     */
    'get /signin/twitter': 'SigninController.twitter',

    /**
     *
     * @api {get} /api/google User Google Signin
     * @apiDescription User signin with google
     * @apiGroup User
     *
     */
    'get /signin/google': 'SigninController.google',

}
