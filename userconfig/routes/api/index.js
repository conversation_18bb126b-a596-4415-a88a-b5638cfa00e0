
const _ = require('lodash');

const clubSafeguardRoutes = require('./club_safeguard');
const customPaymentRoutes = require('./custom-payment');
const eswRoutes = require('./esw');
const homeRoutes = require('./home');
const ncsaRoutes = require('./ncsa');
const officialsScheduleRoutes = require('./officials-schedule');
const onlineCheckinRoutes = require('./online-checkin');
const swbRoutes = require('./swb');
const thsRoutes = require('./ths');
const tpcRoutes = require('./tpc');
const tryoutRoutes = require('./tryout');
const uaExportRoutes = require('./ua');
const swtRoutes = require('./swt');
const swrRoutes = require('./swr');
const officialAppRoutes = require('./official-app');
const eventCheckinOfficialRoutes = require('./event-checkin-official');
const housingRoutes = require('./housing');
const otherRoutes = require('./other');
const acsRoutes = require('./acs');
const swtAppRoutes = require('./swt-app');
const emailRoutes = require('./email');
const emailEditorImageRoutes = require('./email-editor-image');
const ballerTvRoutes = require('./baller-tv');
const eventConnectRoutes = require('./eventconnect');
const volleyStationRoutes = require('./volley-station');

module.exports = _.defaults(
    {}, clubSafeguardRoutes, customPaymentRoutes, eswRoutes, homeRoutes, ncsaRoutes, officialsScheduleRoutes,
    onlineCheckinRoutes, swbRoutes, thsRoutes, tpcRoutes, tryoutRoutes, uaExportRoutes, swtRoutes, swrRoutes,
    officialAppRoutes, eventCheckinOfficialRoutes, housingRoutes, otherRoutes, acsRoutes, swtAppRoutes, emailRoutes,
    emailEditorImageRoutes, ballerTvRoutes, eventConnectRoutes, volleyStationRoutes
)
