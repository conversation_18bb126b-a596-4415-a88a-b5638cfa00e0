

module.exports = {
    /**
     *
     * @api {get} /api/swb/v1/eventlist Events List
     * @apiDescription Get not deleted events list
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/eventlist': 'API/SWBController.eventlist',

    /**
     *
     * @api {get} /api/swb/v1/divisions/:event/:timestamp Event Divisions List
     * @apiDescription Get event divisions list
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/divisions/:event/:timestamp': 'API/SWBController.divisions',

    /**
     *
     * @api {get} /api/swb/v1/clubs/:event/:timestamp Event Clubs List
     * @apiDescription Get event clubs list
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/clubs/:event/:timestamp': 'API/SWBController.clubs',

    /**
     *
     * @api {get} /api/swb/v1/teams/:event/:timestamp Event Teams List
     * @apiDescription Get event teams list
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/teams/:event/:timestamp': 'API/SWBController.teams',

    /**
     *
     * @api {get} /api/swb/v1/teams/:event/:timestamp/:team Event Team Info
     * @apiDescription Get event team info
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/teams/:event/:timestamp/:team': 'API/SWBController.teams',

    /**
     *
     * @api {get} /api/swb/v1/athletes/:event/:timestamp Event Athletes List
     * @apiDescription Get event athletes list
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/athletes/:event/:timestamp': 'API/SWBController.athletes',

    /**
     *
     * @api {get} /api/swb/v1/athletes/:event/:timestamp/:rowId Event Athlete Info
     * @apiDescription Get event athlete info
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/athletes/:event/:timestamp/:rowId': 'API/SWBController.athletes',

    /**
     *
     * @api {get} /api/swb/v1/journal_count/:event Event Journal Data
     * @apiDescription Get event journal data
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/journal_count/:event': 'API/SWBController.journal_count',
    //'GET /api/swb/v1/staff/:event/:timestamp': 'API/SWBController.staff',
    //'GET /api/swb/v1/staff/:event/:timestamp/:staffId': 'API/SWBController.staff',

    /**
     *
     * @api {get} /api/swb/v1/officials/:event/:timestamp Event Officials List
     * @apiDescription Get event officials list
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/officials/:event/:timestamp': 'API/SWBController.officials',

    /**
     *
     * @api {get} /api/swb/v1/officials/:event/:timestamp/:rowId Event Official Info
     * @apiDescription Get event official info
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/officials/:event/:timestamp/:rowId': 'API/SWBController.officials',

    /**
     *
     * @api {get} /api/swb/v1/event/:event/clear-chache Event Official Info (typo)
     * @apiDescription Clear sw-events cache for specific event
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/event/:event/clear-chache': 'API/SWBController.clearEventCache',

    /**
     *
     * @api {get} /api/swb/v1/event/:event/clear-cache Clear Event Redis Cache
     * @apiDescription Clear sw-events cache for specific event
     * @apiGroup SWB - SW Builder
     *
     */
    'GET /api/swb/v1/event/:event/clear-cache': 'API/SWBController.clearEventCache',

    /**
     *
     * @api {post} /api/swb/v1/teams/:event/add Add Team
     * @apiDescription Add custom teams to event
     * @apiGroup SWB - SW Builder
     *
     */
    'POST /api/swb/v1/teams/:event/add': {
        controller: 'API/SWBController',
        action: 'addTeamsManually',
        csrf: false
    },
}
