
const WEBPOINT_CTRL = 'Admin/WebpointController';

module.exports = {
    'GET /admin/webpoint'                   : {
        controller: WEBPOINT_CTRL,
        action: 'index',
        csrf: false
    },
    'POST /api/admin/webpoint/members'      : {
        controller: WEBPOINT_CTRL,
        action: 'club_mbrs',
        csrf: false
    },
    'POST /api/admin/webpoint/run_parser'   : {
        controller: WEBPOINT_CTRL,
        action: 'runParser',
        csrf: false
    },
    'get /api/admin/webpoint/staff-update'  : `${WEBPOINT_CTRL}.updateStaff`,
    'get /api/admin/webpoint/event/:event/staff-update' : `${WEBPOINT_CTRL}.updateStaffOnEvent`,
    'get /api/admin/webpoint/member/:usav/find'         : `${WEBPOINT_CTRL}.findUSAVData`,
    'get /api/admin/webpoint/staff/:staff/staff-update' : `${WEBPOINT_CTRL}.findUSAVData`,
}
