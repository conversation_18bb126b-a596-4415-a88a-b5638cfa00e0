

const _ = require('lodash');

const clubMembers = require('./club-members');
const db = require('./db');
const integrityTests = require('./integrity-tests');
const payments = require('./payments');
const redis = require('./redis');
const sportsengine = require('./sportsengine');
const statistics = require('./statistics');
const stripe = require('./stripe');
const tickets = require('./tickets');
const users = require('./users');
const webpoint = require('./webpoint');
const ban = require('./ban');
const dispute = require('./dispute');
const aau = require('./aau');

module.exports = _.defaults({
    /**
     *
     * @api {get} /api/admin/monitoring DB Stats Activity
     * @apiDescription Returns pg_stat_activity
     * @apiGroup Admin
     *
     */
    'GET /api/admin/monitoring': 'Admin/MonitoringController.index',

    /**
     *
     * @api {get} /api/admin/events Events List
     * @apiDescription Returns events list (id / name / date start) with min start date 2 days ago
     * @apiGroup Admin
     *
     */
    'GET /api/admin/events': 'Admin/EventController.index',

    /**
     *
     * @api {get} /api/admin/db/age/set Set Athletes Age
     * @apiDescription Updates age for all athletes with empty age field using birthdate field value
     * @apiGroup Admin
     *
     */
    'get /api/admin/db/age/set': 'Admin/DbController.set_age',

    /**
     *
     * @api {get} /sw/admin Admin Main Page Template (not used)
     * @apiDescription Returns rendered ejs template with main admin page (not used)
     * @apiGroup Admin
     *
     */
    'GET /sw/admin': 'Admin/AdminController.index',

    /**
     *
     * @api {get} /api/admin/official/:official/usav Member USAV Data (deprecated)
     * @apiDescription Returns USAV data for member from Webpoint (deprecated)
     * @apiGroup Admin
     *
     */
    'GET /api/admin/official/:official/usav': 'Admin/OfficialsController.getUSAVInfo',

    /**
     *
     * @api {get} /api/admin/teams/invalid/memberscount Teams with Small Members Count
     * @apiDescription Returns teams list where athletes count < 6 or staffers count < 3
     * @apiGroup Admin
     *
     */
    'GET /api/admin/teams/invalid/memberscount': 'Admin/EventTeamsController.find_empty_members',

    /**
     *
     * @api {get} /api/admin/club/teams/norank Teams with Empty Rank
     * @apiDescription Returns teams list where empty rank
     * @apiGroup Admin
     *
     */
    'GET /api/admin/club/teams/norank': 'Admin/MasterTeamsController.find_empty_rank',

    /**
     *
     * @api {get} /api/admin/club/teams/norank Fill Rank for Teams with Empty Rank (deprecated)
     * @apiDescription Fills rank for teams list where rank is empty using USAV code (deprecated)
     * @apiGroup Admin
     *
     */
    'GET /api/admin/club/teams/norank/fix': 'Admin/MasterTeamsController.fix_teams_with_empty_rank',
}, clubMembers, db, integrityTests, payments, redis, sportsengine, statistics, stripe, tickets, users, webpoint, ban,
    dispute, aau);
