
const PAYMENTS_CTRL = 'Admin/PaymentsController';

module.exports = {
    /**
     *
     * @api {get} /api/admin/payments Payments List
     * @apiDescription Returns payments list (all or for specific event)
     * @apiGroup Admin Payments
     *
     */
    'GET /api/admin/payments': `${PAYMENTS_CTRL}.index`,

    /**
     *
     * @api {get} /api/admin/payments/:payment/refunded Does Payment Refunded in Stripe
     * @apiDescription Checks if payment refunded in Stripe
     * @apiGroup Admin Payments
     *
     */
    'GET /api/admin/payments/:payment/refunded': `${PAYMENTS_CTRL}.refunded`,

    /**
     *
     * @api {get} /api/admin/payments/teams/all Roster Teams List
     * @apiDescription Returns roster teams list (can be filtered by event)
     * @apiGroup Admin Payments
     *
     */
    'GET /api/admin/payments/teams/all': `${PAYMENTS_CTRL}.roster_teams`,

    /**
     *
     * @api {get} /api/admin/team/:team/payments/check Find Paid Teams without Payments
     * @apiDescription Returns teams that marked as paid but doesn't have actual payments
     * @apiGroup Admin Payments
     *
     */
    'GET /api/admin/team/:team/payments/check': `${PAYMENTS_CTRL}.check_team_payment`,

    /**
     *
     * @api {get} /api/admin/payment/:payment/card/check Check Card Payment Data in Stripe
     * @apiDescription Check if payment has correct charge and its data equals to charge data
     * @apiGroup Admin Payments
     *
     */
    'GET /api/admin/payment/:payment/card/check': `${PAYMENTS_CTRL}.check_card_payment`,

    /**
     *
     * @api {get} /api/admin/payments/ban_disputes Ban Lost Disputes
     * @apiDescription Fetches lost disputes from Stripe and proceed ban in SW
     * @apiGroup Admin Payments
     *
     */
    'GET /api/admin/payments/ban_disputes': `${PAYMENTS_CTRL}.banLostDisputes`,
}
