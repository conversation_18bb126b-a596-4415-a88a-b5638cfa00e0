'use strict';

const CTRL = 'AEM/AdminController.';

module.exports = {
    /**
     *
     * @api {get} /api/aem-admin/init Initial AEM Data
     * @apiDescription Returns admin templates list and initial BeeEditor settings
     * @apiGroup AEM Admin
     *
     */
	'GET /api/aem-admin/init': `${CTRL}getInitData`,

    /**
     *
     * @api {get} /api/aem-admin/tmpl/:tmpl/preview Template HTML for Preview
     * @apiDescription Returns template's HTML for preview mode
     * @apiGroup AEM Admin
     *
     */
	'GET /api/aem-admin/tmpl/:tmpl/preview': `${CTRL}getTmplHTMLforPreview`,

    /**
     *
     * @api {post} /api/aem-admin/tmpl/:tmpl/duplicate Duplicate Template
     * @apiDescription Creates a copy of selected template
     * @apiGroup AEM Admin
     *
     */
	'POST /api/aem-admin/tmpl/:tmpl/duplicate': `${CTRL}duplicateTmpl`,

    /**
     *
     * @api {get} /api/aem-admin/tmpl/:tmpl Template Data
     * @apiDescription Returns specific template data
     * @apiGroup AEM Admin
     *
     */
	'GET /api/aem-admin/tmpl/:tmpl': `${CTRL}getTmpl`,

    /**
     *
     * @api {put} /api/aem-admin/tmpl/:tmpl Update Template
     * @apiDescription Updates specific template
     * @apiGroup AEM Admin
     *
     */
	'PUT /api/aem-admin/tmpl/:tmpl': `${CTRL}updateTmpl`,

    /**
     *
     * @api {post} /api/aem-admin/tmpl Create Template
     * @apiDescription Creates new template
     * @apiGroup AEM Admin
     *
     */
	'POST /api/aem-admin/tmpl': `${CTRL}createTmpl`,

    /**
     *
     * @api {delete} /api/aem-admin/tmpl/:tmpl Remove Template
     * @apiDescription Removes specific template
     * @apiGroup AEM Admin
     *
     */
	'DELETE /api/aem-admin/tmpl/:tmpl': `${CTRL}deleteTmpl`,

    /**
     *
     * @api {post} /api/aem-admin/tmpl/:tmpl/send Test Send
     * @apiDescription Sends rendered template in test mode
     * @apiGroup AEM Admin
     *
     */
	'POST /api/aem-admin/tmpl/:tmpl/send': `${CTRL}testSend`,

    /**
     *
     * @api {get} /api/aem-admin/:group/types Email Template Group Types List
     * @apiDescription Returns all email template type of specific template group
     * @apiGroup AEM Admin
     *
     */
    'GET /api/aem-admin/:group/types': `${CTRL}getTypesOfGroup`,

    /**
     *
     * @api {put} /api/aem-admin/tmpl/:tmpl/publish Publish Template
     * @apiDescription Set template published (visible for users)
     * @apiGroup AEM Admin
     *
     */
    'PUT /api/aem-admin/tmpl/:tmpl/publish': `${CTRL}publishTmpl`,

    /**
     *
     * @api {get} /api/aem-admin/sending/:group/available-tmpls Templates List Allowed for Sending
     * @apiDescription Return all templates allowed for sending (not deleted / published / valid)
     * @apiGroup AEM Admin
     *
     */
    'GET /api/aem-admin/sending/:group/available-tmpls': `${CTRL}templatesForSending`,

    /**
     *
     * @api {post} /api/aem-admin/:group/send-email Send Emails
     * @apiDescription Send emails to receivers of specific template group
     * @apiGroup AEM Admin
     *
     */
    'POST /api/aem-admin/:group/send-email': `${CTRL}sendEmails`
};
