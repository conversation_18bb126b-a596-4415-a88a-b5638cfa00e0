# VolleyStation API Rate Limiting Solution

## Overview

This document provides a complete implementation of Redis-based rate limiting for the VolleyStation API endpoints. The solution protects against excessive usage while maintaining the existing authentication and authorization flow.

**Key Features:**
- Token-based rate limiting (1000 requests per minute per token)
- Redis-backed for distributed environments
- Graceful error handling with fail-open approach
- Comprehensive monitoring and logging
- Zero breaking changes to existing API

## Implementation Summary

### 1. Dependencies Added

```bash
npm install rate-limiter-flexible
```

**Package:** `rate-limiter-flexible@^7.2.0`
- Production-ready Redis rate limiting
- Compatible with existing Redis 2.8.0 infrastructure
- Supports distributed rate limiting

### 2. Configuration Changes

**File:** `config/volleyStation.js`

```javascript
module.exports.volleyStation = {
    apiKey: 'vs_dev_2eec4d4f84c8fad36784c3ceb5f2ef6113745fe838356675ba4a3f6837a74dea',
    
    rateLimit: {
        points: 1000,        // Number of requests allowed
        duration: 60,        // Per 60 seconds (1 minute)
        blockDuration: 60,   // Block for 60 seconds when exceeded
    }
};
```

### 3. Policy Chain Update

**File:** `config/policies.js`

```javascript
// Added import
const volleyStationRateLimit = require('../api/policies/volley-station/rateLimit');

// Updated policy chain
'v2/API/volley-station/*': [volleyStationAuth, volleyStationRateLimit, volleyStationEventAccess],
```

**Policy Execution Order:**
1. `volleyStationAuth` - Validates authentication token
2. `volleyStationRateLimit` - Applies rate limiting (NEW)
3. `volleyStationEventAccess` - Validates event access permissions

### 4. Rate Limiting Policy

**File:** `api/policies/volley-station/rateLimit.js`

**Key Features:**
- **Redis Key Pattern:** `rate-limit:volley-station:{token_hash}`
- **Token Hashing:** SHA-256 hash (first 8 characters) for privacy
- **Fail-Open Design:** Allows requests through if Redis is unavailable
- **Comprehensive Logging:** Tracks violations and errors
- **Standard Headers:** Includes `Retry-After`, `X-RateLimit-*` headers

## Rate Limiting Behavior

### Success Response
When within limits, requests proceed normally with added metadata:

```javascript
// Added to req.rateLimit
{
    limit: 1000,
    remaining: 995,
    resetTime: "2024-01-15T10:31:00Z",
    retryAfter: null
}
```

### Rate Limit Exceeded Response
**HTTP Status:** `429 Too Many Requests`

```json
{
    "error": "Rate limit exceeded",
    "message": "Too many requests for this API token",
    "retryAfter": 60,
    "limit": {
        "requests": 1000,
        "window": "60 seconds",
        "remaining": 0,
        "resetTime": "2024-01-15T10:31:00Z"
    }
}
```

**Response Headers:**
```
Retry-After: 60
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 0
X-RateLimit-Reset: 2024-01-15T10:31:00Z
```

## Redis Integration

### Connection Strategy
- **Reuses existing Redis configuration:** `sails.config.connections.redis`
- **No additional connections required**
- **Automatic connection management with graceful shutdown**

### Key Management
- **Namespace:** `rate-limit:volley-station:`
- **Key Pattern:** `rate-limit:volley-station:{token_hash}`
- **Automatic Expiration:** TTL based on rate limit duration
- **Memory Efficient:** No manual cleanup required

### Example Redis Keys
```
rate-limit:volley-station:a1b2c3d4  # Hashed token
```

## Error Handling

### Fail-Open Design
The rate limiter is designed to fail open, ensuring API availability:

1. **Redis Connection Failure:** Allows requests through
2. **Rate Limiter Initialization Error:** Allows requests through
3. **Unexpected Errors:** Logs error and allows requests through
4. **Missing Token:** Passes to auth policy for proper error handling

### Logging
```javascript
// Rate limit violation
sails.log.warn('VolleyStation Rate Limit Exceeded:', {
    tokenHash: 'a1b2c3d4',
    path: '/api/v2/volley-station/123/schedule',
    ip: '***********',
    userAgent: 'Mozilla/5.0...'
});

// Redis errors
sails.log.error('VolleyStation Rate Limiter Redis Error:', error);
```

## Testing Procedures

### 1. Unit Testing

**Test Rate Limiting Logic:**
```bash
# Test within limits
curl -H "Authorization: vs_dev_2eec4d4f84c8fad36784c3ceb5f2ef6113745fe838356675ba4a3f6837a74dea" \
     http://localhost:1337/api/v2/volley-station/123/schedule

# Expected: 200 OK with data
```

**Test Rate Limit Exceeded:**
```bash
# Send 1001 requests rapidly
for i in {1..1001}; do
    curl -H "Authorization: vs_dev_2eec4d4f84c8fad36784c3ceb5f2ef6113745fe838356675ba4a3f6837a74dea" \
         http://localhost:1337/api/v2/volley-station/123/schedule
done

# Expected: First 1000 return 200 OK, 1001st returns 429
```

### 2. Integration Testing

**Test Policy Chain:**
```bash
# Test without token (should return 401)
curl http://localhost:1337/api/v2/volley-station/123/schedule

# Test with invalid token (should return 401)
curl -H "Authorization: invalid_token" \
     http://localhost:1337/api/v2/volley-station/123/schedule

# Test with valid token (should return 200 or rate limit)
curl -H "Authorization: vs_dev_2eec4d4f84c8fad36784c3ceb5f2ef6113745fe838356675ba4a3f6837a74dea" \
     http://localhost:1337/api/v2/volley-station/123/schedule
```

### 3. Redis Monitoring

**Check Redis Keys:**
```bash
redis-cli
> KEYS rate-limit:volley-station:*
> TTL rate-limit:volley-station:a1b2c3d4
> GET rate-limit:volley-station:a1b2c3d4
```

**Monitor Rate Limiting:**
```bash
redis-cli MONITOR | grep "rate-limit:volley-station"
```

## Deployment Instructions

### 1. Pre-Deployment Checklist

- [ ] `rate-limiter-flexible` package installed
- [ ] Configuration added to `config/volleyStation.js`
- [ ] Policy file created at `api/policies/volley-station/rateLimit.js`
- [ ] Policy chain updated in `config/policies.js`
- [ ] Redis connection tested and working

### 2. Deployment Steps

1. **Deploy Code Changes:**
   ```bash
   # Deploy all modified files
   git add config/volleyStation.js
   git add config/policies.js
   git add api/policies/volley-station/rateLimit.js
   git add package.json
   git commit -m "Add VolleyStation API rate limiting"
   ```

2. **Install Dependencies:**
   ```bash
   npm install
   ```

3. **Restart Application:**
   ```bash
   # Development
   npm start

   # Production
   pm2 restart app
   ```

4. **Verify Deployment:**
   ```bash
   # Check logs for initialization
   tail -f logs/app.log | grep "VolleyStation Rate Limiter"
   
   # Expected: "VolleyStation Rate Limiter initialized successfully"
   ```

### 3. Rollback Plan

**Immediate Rollback:**
```javascript
// In config/policies.js, remove rate limiting
'v2/API/volley-station/*': [volleyStationAuth, volleyStationEventAccess],
```

**Complete Rollback:**
1. Remove rate limiting from policy chain
2. Remove `api/policies/volley-station/rateLimit.js`
3. Remove rate limit configuration from `config/volleyStation.js`
4. Restart application

## Monitoring and Maintenance

### Key Metrics to Monitor

1. **Rate Limit Violations:**
   - Monitor logs for "Rate Limit Exceeded" messages
   - Track violation frequency and patterns

2. **Redis Performance:**
   - Monitor Redis memory usage
   - Track Redis operation latency
   - Monitor Redis connection health

3. **API Performance:**
   - Monitor response times for VolleyStation endpoints
   - Track error rates and success rates

### Alerting Recommendations

**Critical Alerts:**
- High rate limit violation rates (>10% of requests)
- Redis connection failures
- Rate limiter initialization failures

**Warning Alerts:**
- Unusual token usage patterns
- Redis memory usage growth
- Slow Redis operations (>100ms)

### Configuration Tuning

**Adjust Rate Limits:**
```javascript
// In config/volleyStation.js
rateLimit: {
    points: 2000,        // Increase limit
    duration: 60,        // Keep window
    blockDuration: 30,   // Reduce penalty
}
```

**Monitor and Adjust:**
1. Start with conservative limits (1000/minute)
2. Monitor actual usage patterns
3. Adjust based on legitimate usage needs
4. Consider different limits for different endpoints if needed

## Security Considerations

### Token Privacy
- Tokens are hashed using SHA-256 before storage in Redis
- Only first 8 characters of hash used for brevity
- No plain text tokens stored in Redis

### DDoS Protection
- Rate limiting provides application-level protection
- Complements infrastructure-level DDoS protection
- Prevents resource exhaustion attacks

### Monitoring Security
- All rate limit violations are logged with metadata
- Suspicious patterns can be detected and investigated
- Token usage patterns provide security insights

---

## Summary

The VolleyStation API rate limiting solution is now fully implemented and ready for deployment. The solution provides:

✅ **1000 requests per minute per token rate limiting**
✅ **Redis-backed distributed rate limiting**
✅ **Graceful error handling with fail-open design**
✅ **Comprehensive logging and monitoring**
✅ **Zero breaking changes to existing API**
✅ **Production-ready with rollback plan**

**Total Implementation Time:** ~2 hours
**Risk Level:** Low (fail-open design ensures availability)
**Dependencies:** Uses existing Redis infrastructure
