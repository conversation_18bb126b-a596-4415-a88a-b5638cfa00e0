# VolleyStation Integration Implementation Plan

## Executive Summary

This document outlines the implementation plan for integrating VolleyStation with the SportWrench platform. The integration will provide API endpoints for VolleyStation to retrieve event data (schedule, athletes, staff) and submit results data, following the established SailsJS Actions pattern used in the v2 API controllers.

## Codebase Analysis Results

### Controller Architecture Patterns

**Legacy Pattern (API directory):**
- Location: `api/controllers/API/`
- Example: `TPCController.js` - Traditional SailsJS controller functions
- Used for older integrations like TPC, NCSA, SWB

**Modern Pattern (v2 directory):**
- Location: `api/controllers/v2/API/`
- Example: `baller-tv/events.js` - SailsJS Actions pattern
- **REQUIRED for all new implementations per core engineer mandate**

### Existing Third-Party Integration Patterns

1. **BallerTV Integration** (`api/controllers/v2/API/baller-tv/`)
   - Uses SailsJS Actions pattern
   - Simple API key authentication
   - Event filtering with `teams_settings->>'baller_tv_available'`

2. **ACS Integration** (`api/controllers/v2/API/acs/`)
   - Complex authentication with custom policies
   - Multiple endpoints for different data types

3. **TPC Integration** (`api/controllers/API/TPCController.js`)
   - Legacy pattern but provides reference data structure
   - Uses `UAExportService.getSchedule()` for data retrieval

### Authentication Patterns

**Simple API Key Pattern (Recommended for VolleyStation):**
```javascript
// Policy: api/policies/volley-station/auth.js
if(authorization === sails.config.volleyStation.apiKey) {
    next();
} else {
    res.status(401).json({ error: 'Authorization token invalid' });
}
```

**Configuration Pattern:**
```javascript
// config/volleyStation.js
module.exports.volleyStation = {
    apiKey: 'vs_api_key_to_be_configured'
}
```

## Recommended Architecture

### Directory Structure
```
api/controllers/v2/API/volley-station/
├── events.js           # GET - List enabled events
├── schedule.js         # GET - Event schedule data (paginated)
├── team-roster.js      # GET - Event team roster data (paginated)
└── results.js         # POST - Submit results data

api/policies/volley-station/
└── auth.js            # Authentication policy

userconfig/routes/api/
└── volley-station.js  # Route definitions

config/
└── volleyStation.js   # Configuration file
```

### Endpoint Design

**Base URL Pattern:** `/api/volley-station/v1/`

1. **GET /api/volley-station/v1/events**
   - Returns events with `enable_volley_station: true`
   - Similar to BallerTV events endpoint

2. **GET /api/volley-station/v1/events/:eventId/schedule**
   - Returns schedule data using `UAExportService.getSchedule()`
   - Includes matches, teams, courts, timing
   - Supports pagination with `page` and `limit` query parameters

3. **GET /api/volley-station/v1/events/:eventId/team-roster**
   - Returns unified team roster data including athletes, staff, and team information
   - Supports pagination with `page` and `limit` query parameters
   - Filtered by event and team status

4. **POST /api/volley-station/v1/events/:eventId/results**
   - Accepts match results and scores
   - Validates and stores result data

### Authorization Implementation

**Event-Level Authorization:**
- Check `enable_volley_station: true` in dedicated `event` table column
- Pattern: `where('e.enable_volley_station', true)`
- Return 403 Forbidden if not enabled

**Database Schema Addition:**
```sql
-- Add new column to event table
ALTER TABLE event ADD COLUMN enable_volley_station BOOLEAN DEFAULT FALSE;

-- Create index for performance
CREATE INDEX idx_event_enable_volley_station ON event(enable_volley_station) WHERE enable_volley_station = true;
```

**API Key Authentication:**
- Header: `Authorization: vs_api_key_value`
- Validated against `sails.config.volleyStation.apiKey`
- Applied via policy to all VolleyStation endpoints

## Rate Limiting Implementation

Based on existing codebase patterns, implement rate limiting using Redis-based approach similar to the auto_payout_process queue configuration:

**Configuration Pattern:**
```javascript
// config/volleyStation.js
module.exports.volleyStation = {
    apiKey: 'vs_api_key_to_be_configured',
    rateLimiting: {
        enabled: true,
        maxRequests: 100,        // requests per window
        windowMs: 60 * 1000,     // 1 minute window
        skipSuccessfulRequests: false
    }
}
```

**Rate Limiting Policy:**
```javascript
// api/policies/volley-station/rate-limit.js
const redis = require('redis');
const client = redis.createClient(sails.config.connections.redis);

module.exports = async function (req, res, next) {
    if (!sails.config.volleyStation.rateLimiting.enabled) {
        return next();
    }

    const { maxRequests, windowMs } = sails.config.volleyStation.rateLimiting;
    const key = `volley_station_rate_limit:${req.getIP()}`;

    try {
        const current = await client.incr(key);

        if (current === 1) {
            await client.expire(key, Math.ceil(windowMs / 1000));
        }

        if (current > maxRequests) {
            return res.status(429).json({
                error: 'Rate limit exceeded',
                message: `Maximum ${maxRequests} requests per minute allowed`,
                retryAfter: windowMs / 1000
            });
        }

        res.setHeader('X-RateLimit-Limit', maxRequests);
        res.setHeader('X-RateLimit-Remaining', Math.max(0, maxRequests - current));

        next();
    } catch (err) {
        loggers.errors_log.error('Rate limiting error:', err);
        next(); // Allow request on Redis error
    }
};
```

## Pagination Implementation

Following existing codebase patterns from ACS teams list and payment services:

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 50, max: 100)

**Response Format:**
```javascript
{
    "data": [...],
    "pagination": {
        "page": 1,
        "limit": 50,
        "total": 150,
        "totalPages": 3,
        "hasNext": true,
        "hasPrev": false
    }
}
```

**Implementation Pattern:**
```javascript
// In controller actions
const page = Math.max(1, parseInt(inputs.page) || 1);
const limit = Math.min(100, Math.max(1, parseInt(inputs.limit) || 50));
const offset = (page - 1) * limit;

// Apply to query
query.limit(limit).offset(offset);

// Count total for pagination
const countQuery = query.clone().clearSelect().clearOrder().count('* as total');
const [{ total }] = await Db.query(countQuery);

// Return paginated response
return {
    data: results,
    pagination: {
        page,
        limit,
        total: parseInt(total),
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
    }
};
```

## Implementation Steps

### Phase 1: Foundation Setup
1. Add `enable_volley_station` column to event table
2. Create configuration file `config/volleyStation.js` with rate limiting config
3. Create authentication policy `api/policies/volley-station/auth.js`
4. Create rate limiting policy `api/policies/volley-station/rate-limit.js`
5. Create route definitions `userconfig/routes/api/volley-station.js`
6. Create base controller directory structure

### Phase 2: Data Retrieval Endpoints
1. Implement events listing endpoint
2. Implement schedule endpoint with pagination (leverage existing `UAExportService`)
3. Implement team-roster endpoint with pagination (unified athletes and staff data)
4. Add proper input validation and error handling

### Phase 3: Results Submission
1. Design results data schema
2. Implement results submission endpoint
3. Add data validation and storage logic
4. Implement proper error responses

### Phase 4: Testing & Documentation
1. Create comprehensive API tests
2. Document endpoint specifications
3. Test authorization scenarios
4. Performance testing with large datasets

## Data Structure Reference

The existing TPC integration provides ~75% of the required data structure through `UAExportService.getSchedule()`. Key fields include:

- `match_uuid`, `event`, `match_id`
- `date_time`, `court`, `court_alpha`
- `team_1_name`, `team_2_name`, `ref_name`
- `master_team_id_1`, `master_team_id_2`
- `results` (JSON object with set scores)

Additional athlete and staff data will need separate service methods following similar patterns.

## Configuration Management

**Development/Staging:**
```javascript
// config/volleyStation.js
module.exports.volleyStation = {
    apiKey: 'vs_dev_api_key_12345'
}
```

**Production:**
- API key managed through environment configuration
- Loaded via `config/env/production.js` pattern
- Stored securely in deployment configuration

## Security Considerations

1. **API Key Security:** Store in environment variables, not in code
2. **Rate Limiting:** Implemented using Redis-based approach (see Rate Limiting section)
3. **Input Validation:** Strict validation on all POST endpoints
4. **Event Authorization:** Always verify event access permissions using dedicated column
5. **Audit Logging:** Log all API access for monitoring

## Next Steps

1. Review and approve this implementation plan
2. Create detailed API specification document
3. Begin Phase 1 implementation
4. Coordinate with VolleyStation team on data requirements
5. Plan testing and deployment strategy

---

*This plan follows established SportWrench patterns and ensures consistency with existing third-party integrations while meeting the SailsJS Actions requirement for new code.*
